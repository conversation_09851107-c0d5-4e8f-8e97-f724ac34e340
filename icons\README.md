# 图标文件说明

## 当前状态
为了避免编译错误，所有图标引用已被暂时注释。软件可以正常编译和运行，但没有图标显示。

## 如何添加图标

### 第一步：准备图标文件
在此目录下添加以下图标文件：
- `app_icon.ico` - 应用程序主图标（32x32或48x48像素）
- `open.png` - 打开文件图标（16x16或24x24像素）
- `save.png` - 保存文件图标（16x16或24x24像素）
- `exit.png` - 退出程序图标（16x16或24x24像素）
- `about.png` - 关于对话框图标（16x16或24x24像素）
- `grayscale.png` - 灰度化处理图标（16x16或24x24像素）
- `binary.png` - 二值化处理图标（16x16或24x24像素）
- `filter.png` - 滤波处理图标（16x16或24x24像素）
- `gamma.png` - 伽马变换图标（16x16或24x24像素）
- `edge.png` - 边缘检测图标（16x16或24x24像素）

### 第二步：修改资源文件
在 `resources.qrc` 文件中取消注释图标引用：
```xml
<RCC>
    <qresource prefix="/icons">
        <file>icons/open.png</file>
        <file>icons/save.png</file>
        <file>icons/exit.png</file>
        <file>icons/about.png</file>
        <file>icons/grayscale.png</file>
        <file>icons/binary.png</file>
        <file>icons/filter.png</file>
        <file>icons/gamma.png</file>
        <file>icons/edge.png</file>
        <file>icons/app_icon.ico</file>
    </qresource>
</RCC>
```

### 第三步：修改项目文件
在 `findWork.pro` 文件中取消注释应用程序图标：
```
RC_ICONS = icons/app_icon.ico
```

### 第四步：修改代码
在 `mainwindow.cpp` 文件中取消注释所有图标引用。

## 图标资源推荐
- Icons8 (https://icons8.com/)
- Flaticon (https://www.flaticon.com/)
- Material Design Icons
- 或使用Qt Creator内置的图标资源

## 注意事项
- 确保图标文件格式正确（PNG/ICO）
- 建议使用透明背景的PNG图标
- 图标尺寸要适中，避免过大影响界面美观
