# 数字图像处理软件 V1.0

**开发者：** 丁浩君
**学号：** 249400209
**工程名：** ImageProcessor_DingHaoJun_249400209

## 项目概述

这是一个基于Qt Creator开发的简易数字图像处理软件，实现了多种常用的图像处理功能。

## 功能特性

### 1. 文件操作
- ✅ 支持单张图片打开（Ctrl+O）
- ✅ 支持多张图片打开（Ctrl+Shift+O）
- ✅ 图片保存功能（Ctrl+S）
- ✅ 支持多种图片格式：PNG, JPG, JPEG, BMP, GIF, TIFF

### 2. 图像处理功能
- ✅ **灰度化处理**：使用(R+G+B)/3算法（Ctrl+G）
- ✅ **二值化处理**：可调阈值滑条控件（Ctrl+B）
- ✅ **3×3均值滤波**：平滑图像噪声（Ctrl+F）
- ✅ **伽马变换**：针对彩色图像的亮度调整（Ctrl+A）
- ✅ **Sobel边缘检测**：检测图像边缘特征（Ctrl+E）

### 3. 界面功能
- ✅ 主窗口框架（非对话框）
- ✅ 完整的菜单栏（文件、编辑、视图、帮助）
- ✅ 工具栏快速访问
- ✅ **画图板风格工具面板**：右侧小图标按钮布局
- ✅ 快捷键支持
- ✅ 状态栏信息显示
- ✅ 实时参数控制面板
- ✅ 图像缩放和查看功能

### 4. 控制面板
- ✅ 二值化阈值滑条（0-255）
- ✅ 伽马变换滑条（0.1-3.0）
- ✅ 实时参数调整

### 5. 视图控制
- ✅ 适应窗口大小（Ctrl+W）
- ✅ 实际大小显示（Ctrl+1）
- ✅ 放大缩小功能（Ctrl+/Ctrl-）
- ✅ 重置到原始图片（Ctrl+R）

### 6. 视频处理功能 (B档功能)
- ✅ **视频文件处理**：支持MP4、AVI、MOV等格式
- ✅ **摄像头实时处理**：支持USB摄像头实时图像处理
- ✅ **多种处理模式**：
  - 原始显示
  - 灰度化处理
  - 二值化处理（可调阈值）
  - Sobel边缘检测
  - 3×3均值滤波
  - 局部马赛克效果
- ✅ **处理控制**：
  - 可调处理间隔（30-500ms）
  - 实时模式切换
  - 播放/暂停/停止控制
  - 进度显示和监控
- ✅ **技术特点**：
  - 基于OpenCV 4.x
  - 自主实现核心算法
  - 支持10秒以上视频处理
  - 实时参数调整

## 编译和运行

### 环境要求
- Qt 5.x 或更高版本
- Qt Creator IDE
- C++11 支持的编译器
- **OpenCV 4.x** (视频处理功能必需)

### 编译步骤
1. **安装OpenCV**：参考 `OpenCV配置指南.md` 安装OpenCV库
2. 打开Qt Creator
3. 打开项目文件 `findWork.pro`
4. 配置编译器（推荐使用MinGW或MSVC）
5. 确认OpenCV路径配置正确
6. 点击"构建"按钮编译项目
7. 运行生成的可执行文件

### 注意事项
- ✅ **当前状态**：所有功能已完整实现，可以正常编译和运行
- ⚠️ **图标文件**：为避免编译错误，图标引用已暂时注释
- 📁 **添加图标**：参考 `icons/README.md` 文件了解如何添加图标
- 🎯 **可执行文件名称**：`ImageProcessor_DingHaoJun_249400209.exe`

## 使用说明

1. **打开图片**：使用"文件"菜单或Ctrl+O打开图片
2. **选择处理功能**：从"编辑"菜单选择需要的图像处理功能
3. **调整参数**：使用右侧控制面板调整二值化阈值或伽马值
4. **查看结果**：处理后的图片会在中央显示区域显示
5. **保存图片**：使用"文件"菜单或Ctrl+S保存处理后的图片
6. **重置图片**：使用Ctrl+R恢复到原始图片

## 关于对话框

软件包含自定义的"关于本软件"对话框，显示：
- 开发者姓名和学号
- 软件功能特性列表
- 版权信息

## 技术实现

- **UI框架**：Qt Widgets
- **图像处理**：QImage类和自定义算法
- **文件操作**：QFileDialog
- **界面布局**：QSplitter, QDockWidget
- **控件**：QSlider, QSpinBox, QLabel等

## 版权信息

版权所有 © 2024 丁浩君图像处理工作室
学号：249400209
