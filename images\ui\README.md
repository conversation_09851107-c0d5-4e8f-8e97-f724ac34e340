# UI 图片目录说明

这个目录存放用户界面相关的图片资源。

## 目录用途

存放应用程序界面美化和功能增强的图片文件，如：
- 应用程序Logo
- 背景图片
- 启动画面
- 装饰性图片

## 推荐的文件

### 基础UI图片
- **logo.png** - 应用程序Logo (128x128 或 256x256)
- **background.png** - 主界面背景图片
- **splash.png** - 启动画面图片

### 可选的装饰图片
- **watermark.png** - 水印图片
- **banner.png** - 横幅图片
- **placeholder.png** - 占位符图片

## 使用方式

这些图片通过 resources.qrc 文件引用，在代码中使用 `:/ui/filename.png` 的方式加载。

例如：
```cpp
// 设置Logo
QPixmap logo(":/ui/logo.png");
ui->logoLabel->setPixmap(logo);

// 设置背景
setStyleSheet("QMainWindow { background-image: url(:/ui/background.png); }");
```

## 图片要求

- **格式**: PNG (推荐，支持透明)、JPG
- **尺寸**: 根据用途确定，不要过大
- **质量**: 高质量，适合界面显示
- **风格**: 与应用程序整体设计风格一致

## 注意事项

- 控制图片文件大小，避免影响程序启动速度
- 使用合适的压缩比例
- 考虑不同屏幕分辨率的适配
- 注意版权问题
