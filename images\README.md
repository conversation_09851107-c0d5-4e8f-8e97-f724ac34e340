# Images 文件夹说明

这个文件夹用于存放项目中使用的图片资源。

## 目录结构

```
images/
├── icons/          # 图标文件
│   ├── toolbar/    # 工具栏图标
│   ├── menu/       # 菜单图标
│   └── app/        # 应用程序图标
├── samples/        # 示例图片
├── backgrounds/    # 背景图片
└── ui/            # UI相关图片
```

## 推荐的图片格式

### 图标文件
- **PNG格式**：支持透明背景，适合图标
- **ICO格式**：Windows应用程序图标
- **SVG格式**：矢量图标，可缩放

### 示例图片
- **JPG/JPEG**：照片类图片
- **PNG**：需要透明背景的图片
- **BMP**：Windows位图格式
- **TIFF**：高质量图片

## 图标尺寸建议

### 工具栏图标
- 16x16 像素（小图标）
- 24x24 像素（中等图标）
- 32x32 像素（大图标）

### 菜单图标
- 16x16 像素（标准菜单图标）

### 应用程序图标
- 16x16, 32x32, 48x48, 64x64, 128x128 像素
- 建议制作多种尺寸的ICO文件

## 示例图片用途

### 测试图像处理功能
- 彩色图片：测试灰度化、伽马变换
- 高对比度图片：测试二值化
- 有噪声的图片：测试滤波功能
- 边缘清晰的图片：测试边缘检测

### 推荐的测试图片类型
1. **Lena图片**：经典的图像处理测试图片
2. **棋盘图**：测试几何变换
3. **渐变图**：测试色彩处理
4. **文字图片**：测试边缘检测
5. **自然风景**：综合测试

## 使用方法

1. 将图片文件放入相应的子文件夹
2. 在 `resources.qrc` 文件中添加资源引用
3. 在代码中使用 `:/images/...` 路径访问

## 注意事项

- 确保图片文件大小适中，避免过大影响程序性能
- 使用有意义的文件名，便于管理
- 保持图标风格一致
- 注意版权问题，使用免费或自制的图片
