#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QApplication>
#include <QScreen>
#include <QMimeData>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QUrl>
#include <QTextStream>
#include <QDebug>
#include <cmath>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::MainWindow), scaleFactor(1.0), imageLoaded(false)
{
    ui->setupUi(this);

    // 设置窗口标题 - 请根据实际姓名和学号修改
    setWindowTitle("数字图像处理软件V1.0 丁浩君 249400209");

    // 设置窗口图标（暂时注释，需要添加实际图标文件）
    // setWindowIcon(QIcon(":/icons/app_icon.ico"));

    // 初始化UI
    setupUI();
    createMenus();
    createToolBars();
    createStatusBar();
    connectSignals();

    // 启用拖放
    setAcceptDrops(true);

    // 初始状态
    updateImageDisplay();
}

MainWindow::~MainWindow()
{
    delete ui;
}

// UI设置函数
void MainWindow::setupUI()
{
    // UI文件已经设置了界面布局，这里只需要做一些额外的配置
    ui->imageLabel->setScaledContents(true);
    ui->imageScrollArea->setBackgroundRole(QPalette::Dark);
}

void MainWindow::createMenus()
{
    // 文件菜单
    fileMenu = menuBar()->addMenu("文件(&F)");

    openAct = new QAction("打开图片(&O)", this);
    openAct->setShortcuts(QKeySequence::Open);
    openAct->setStatusTip("打开一张图片");
    fileMenu->addAction(openAct);

    openMultipleAct = new QAction("打开多张图片(&M)", this);
    openMultipleAct->setShortcut(QKeySequence("Ctrl+Shift+O"));
    openMultipleAct->setStatusTip("打开多张图片");
    fileMenu->addAction(openMultipleAct);

    fileMenu->addSeparator();

    saveAct = new QAction("保存图片(&S)", this);
    saveAct->setShortcuts(QKeySequence::Save);
    saveAct->setStatusTip("保存当前图片");
    saveAct->setEnabled(false);
    fileMenu->addAction(saveAct);

    fileMenu->addSeparator();

    exitAct = new QAction("退出(&X)", this);
    exitAct->setShortcuts(QKeySequence::Quit);
    exitAct->setStatusTip("退出应用程序");
    fileMenu->addAction(exitAct);

    // 编辑菜单
    editMenu = menuBar()->addMenu("编辑(&E)");

    grayscaleAct = new QAction("灰度化(&G)", this);
    grayscaleAct->setShortcut(QKeySequence("Ctrl+G"));
    grayscaleAct->setStatusTip("将图片转换为灰度图");
    grayscaleAct->setEnabled(false);
    editMenu->addAction(grayscaleAct);

    binaryAct = new QAction("二值化(&B)", this);
    binaryAct->setShortcut(QKeySequence("Ctrl+B"));
    binaryAct->setStatusTip("将图片转换为二值图");
    binaryAct->setEnabled(false);
    editMenu->addAction(binaryAct);

    meanFilterAct = new QAction("均值滤波(&F)", this);
    meanFilterAct->setShortcut(QKeySequence("Ctrl+F"));
    meanFilterAct->setStatusTip("应用3x3均值滤波");
    meanFilterAct->setEnabled(false);
    editMenu->addAction(meanFilterAct);

    gammaAct = new QAction("伽马变换(&A)", this);
    gammaAct->setShortcut(QKeySequence("Ctrl+A"));
    gammaAct->setStatusTip("应用伽马变换");
    gammaAct->setEnabled(false);
    editMenu->addAction(gammaAct);

    edgeDetectionAct = new QAction("边缘检测(&E)", this);
    edgeDetectionAct->setShortcut(QKeySequence("Ctrl+E"));
    edgeDetectionAct->setStatusTip("Sobel边缘检测");
    edgeDetectionAct->setEnabled(false);
    editMenu->addAction(edgeDetectionAct);

    editMenu->addSeparator();

    resetAct = new QAction("重置图片(&R)", this);
    resetAct->setShortcut(QKeySequence("Ctrl+R"));
    resetAct->setStatusTip("重置到原始图片");
    resetAct->setEnabled(false);
    editMenu->addAction(resetAct);

    // 视图菜单
    viewMenu = menuBar()->addMenu("视图(&V)");

    fitToWindowAct = new QAction("适应窗口(&W)", this);
    fitToWindowAct->setShortcut(QKeySequence("Ctrl+W"));
    fitToWindowAct->setStatusTip("缩放图片以适应窗口");
    fitToWindowAct->setEnabled(false);
    viewMenu->addAction(fitToWindowAct);

    actualSizeAct = new QAction("实际大小(&1)", this);
    actualSizeAct->setShortcut(QKeySequence("Ctrl+1"));
    actualSizeAct->setStatusTip("显示图片实际大小");
    actualSizeAct->setEnabled(false);
    viewMenu->addAction(actualSizeAct);

    viewMenu->addSeparator();

    zoomInAct = new QAction("放大(&+)", this);
    zoomInAct->setShortcut(QKeySequence::ZoomIn);
    zoomInAct->setStatusTip("放大图片");
    zoomInAct->setEnabled(false);
    viewMenu->addAction(zoomInAct);

    zoomOutAct = new QAction("缩小(&-)", this);
    zoomOutAct->setShortcut(QKeySequence::ZoomOut);
    zoomOutAct->setStatusTip("缩小图片");
    zoomOutAct->setEnabled(false);
    viewMenu->addAction(zoomOutAct);

    // 帮助菜单
    helpMenu = menuBar()->addMenu("帮助(&H)");

    aboutAct = new QAction("关于(&A)", this);
    aboutAct->setStatusTip("关于本软件");
    helpMenu->addAction(aboutAct);
}

void MainWindow::createToolBars()
{
    // 文件工具栏
    fileToolBar = addToolBar("文件");
    fileToolBar->addAction(openAct);
    fileToolBar->addAction(saveAct);
    fileToolBar->addSeparator();
    fileToolBar->addAction(exitAct);

    // 编辑工具栏
    editToolBar = addToolBar("图像处理");
    editToolBar->addAction(grayscaleAct);
    editToolBar->addAction(binaryAct);
    editToolBar->addAction(meanFilterAct);
    editToolBar->addAction(gammaAct);
    editToolBar->addAction(edgeDetectionAct);
    editToolBar->addSeparator();
    editToolBar->addAction(resetAct);

    // 视图工具栏
    viewToolBar = addToolBar("视图");
    viewToolBar->addAction(fitToWindowAct);
    viewToolBar->addAction(actualSizeAct);
    viewToolBar->addSeparator();
    viewToolBar->addAction(zoomInAct);
    viewToolBar->addAction(zoomOutAct);
}

void MainWindow::createStatusBar()
{
    statusBar()->showMessage("就绪");
}

void MainWindow::connectSignals()
{
    // 文件操作信号连接
    connect(openAct, &QAction::triggered, this, &MainWindow::openImage);
    connect(openMultipleAct, &QAction::triggered, this, &MainWindow::openMultipleImages);
    connect(saveAct, &QAction::triggered, this, &MainWindow::saveImage);
    connect(exitAct, &QAction::triggered, this, &MainWindow::exitApplication);

    // 图像处理信号连接（菜单）
    connect(grayscaleAct, &QAction::triggered, this, &MainWindow::convertToGrayscale);
    connect(binaryAct, &QAction::triggered, this, &MainWindow::convertToBinary);
    connect(meanFilterAct, &QAction::triggered, this, &MainWindow::applyMeanFilter);
    connect(gammaAct, &QAction::triggered, this, &MainWindow::applyGammaCorrection);
    connect(edgeDetectionAct, &QAction::triggered, this, &MainWindow::detectEdges);
    connect(resetAct, &QAction::triggered, this, &MainWindow::resetImage);

    // 图像处理信号连接（工具按钮）
    connect(ui->grayscaleBtn, &QPushButton::clicked, this, &MainWindow::convertToGrayscale);
    connect(ui->binaryBtn, &QPushButton::clicked, this, &MainWindow::convertToBinary);
    connect(ui->meanFilterBtn, &QPushButton::clicked, this, &MainWindow::applyMeanFilter);
    connect(ui->gammaBtn, &QPushButton::clicked, this, &MainWindow::applyGammaCorrection);
    connect(ui->edgeDetectionBtn, &QPushButton::clicked, this, &MainWindow::detectEdges);
    connect(ui->resetBtn, &QPushButton::clicked, this, &MainWindow::resetImage);

    // 视图控制信号连接
    connect(fitToWindowAct, &QAction::triggered, this, &MainWindow::fitToWindow);
    connect(actualSizeAct, &QAction::triggered, this, &MainWindow::actualSize);
    connect(zoomInAct, &QAction::triggered, this, &MainWindow::zoomIn);
    connect(zoomOutAct, &QAction::triggered, this, &MainWindow::zoomOut);

    // 帮助信号连接
    connect(aboutAct, &QAction::triggered, this, &MainWindow::showAbout);

    // 控制面板信号连接
    connect(ui->thresholdSlider, &QSlider::valueChanged, this, &MainWindow::onThresholdChanged);
    connect(ui->thresholdSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            ui->thresholdSlider, &QSlider::setValue);
    connect(ui->thresholdSlider, &QSlider::valueChanged,
            ui->thresholdSpinBox, &QSpinBox::setValue);

    connect(ui->gammaSlider, &QSlider::valueChanged, this, [this](int value)
            {
        double gamma = value / 100.0;
        ui->gammaValueLabel->setText(QString("伽马值: %1").arg(gamma, 0, 'f', 1));
        onGammaChanged(gamma); });
}

// 文件操作槽函数实现
void MainWindow::openImage()
{
    QString fileName = QFileDialog::getOpenFileName(this,
                                                    "打开图片", QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
                                                    "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.tiff)");

    if (!fileName.isEmpty())
    {
        QImage image(fileName);
        if (image.isNull())
        {
            QMessageBox::information(this, "图像处理软件",
                                     "无法加载图片 " + fileName + "。");
            return;
        }

        originalImage = image;
        currentImage = image;
        imageLoaded = true;

        displayImage();
        updateImageDisplay();

        statusBar()->showMessage(QString("已加载图片: %1 (%2x%3)")
                                     .arg(QFileInfo(fileName).fileName())
                                     .arg(image.width())
                                     .arg(image.height()));
    }
}

void MainWindow::openMultipleImages()
{
    QStringList fileNames = QFileDialog::getOpenFileNames(this,
                                                          "打开多张图片", QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
                                                          "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.tiff)");

    if (!fileNames.isEmpty())
    {
        // 显示第一张图片
        QString firstName = fileNames.first();
        QImage image(firstName);
        if (image.isNull())
        {
            QMessageBox::information(this, "图像处理软件",
                                     "无法加载图片 " + firstName + "。");
            return;
        }

        originalImage = image;
        currentImage = image;
        imageLoaded = true;

        displayImage();
        updateImageDisplay();

        statusBar()->showMessage(QString("已加载 %1 张图片，当前显示: %2 (%3x%4)")
                                     .arg(fileNames.size())
                                     .arg(QFileInfo(firstName).fileName())
                                     .arg(image.width())
                                     .arg(image.height()));

        // 显示加载的文件列表
        QString fileList = "已加载的图片文件:\n";
        for (const QString &fileName : fileNames)
        {
            fileList += QFileInfo(fileName).fileName() + "\n";
        }
        QMessageBox::information(this, "多文件加载", fileList);
    }
}

void MainWindow::saveImage()
{
    if (!imageLoaded)
    {
        QMessageBox::information(this, "图像处理软件", "没有图片可以保存。");
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this,
                                                    "保存图片", QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
                                                    "PNG文件 (*.png);;JPEG文件 (*.jpg);;BMP文件 (*.bmp)");

    if (!fileName.isEmpty())
    {
        if (currentImage.save(fileName))
        {
            statusBar()->showMessage("图片已保存: " + QFileInfo(fileName).fileName());
        }
        else
        {
            QMessageBox::information(this, "图像处理软件", "保存图片失败。");
        }
    }
}

void MainWindow::exitApplication()
{
    QApplication::quit();
}

// 图像处理槽函数实现
void MainWindow::convertToGrayscale()
{
    if (!imageLoaded)
    {
        QMessageBox::information(this, "图像处理软件", "请先加载图片。");
        return;
    }

    currentImage = processGrayscale(originalImage);
    displayImage();
    statusBar()->showMessage("已应用灰度化处理");
}

void MainWindow::convertToBinary()
{
    if (!imageLoaded)
    {
        QMessageBox::information(this, "图像处理软件", "请先加载图片。");
        return;
    }

    int threshold = ui->thresholdSlider->value();
    currentImage = processBinary(originalImage, threshold);
    displayImage();
    statusBar()->showMessage(QString("已应用二值化处理，阈值: %1").arg(threshold));
}

void MainWindow::applyMeanFilter()
{
    if (!imageLoaded)
    {
        QMessageBox::information(this, "图像处理软件", "请先加载图片。");
        return;
    }

    currentImage = processMeanFilter(originalImage);
    displayImage();
    statusBar()->showMessage("已应用3x3均值滤波");
}

void MainWindow::applyGammaCorrection()
{
    if (!imageLoaded)
    {
        QMessageBox::information(this, "图像处理软件", "请先加载图片。");
        return;
    }

    double gamma = ui->gammaSlider->value() / 100.0;
    currentImage = processGammaCorrection(originalImage, gamma);
    displayImage();
    statusBar()->showMessage(QString("已应用伽马变换，伽马值: %1").arg(gamma, 0, 'f', 1));
}

void MainWindow::detectEdges()
{
    if (!imageLoaded)
    {
        QMessageBox::information(this, "图像处理软件", "请先加载图片。");
        return;
    }

    currentImage = processEdgeDetection(originalImage);
    displayImage();
    statusBar()->showMessage("已应用Sobel边缘检测");
}

// 界面控制槽函数实现
void MainWindow::onThresholdChanged(int value)
{
    if (imageLoaded)
    {
        currentImage = processBinary(originalImage, value);
        displayImage();
        statusBar()->showMessage(QString("二值化阈值: %1").arg(value));
    }
}

void MainWindow::onGammaChanged(double value)
{
    if (imageLoaded)
    {
        currentImage = processGammaCorrection(originalImage, value);
        displayImage();
        statusBar()->showMessage(QString("伽马值: %1").arg(value, 0, 'f', 1));
    }
}

void MainWindow::resetImage()
{
    if (!imageLoaded)
    {
        QMessageBox::information(this, "图像处理软件", "请先加载图片。");
        return;
    }

    currentImage = originalImage;
    displayImage();
    statusBar()->showMessage("已重置到原始图片");
}

void MainWindow::fitToWindow()
{
    if (!imageLoaded)
        return;

    QSize scrollAreaSize = ui->imageScrollArea->size();
    QSize imageSize = currentImage.size();

    double scaleX = (double)scrollAreaSize.width() / imageSize.width();
    double scaleY = (double)scrollAreaSize.height() / imageSize.height();
    scaleFactor = qMin(scaleX, scaleY) * 0.9; // 留一些边距

    displayImage();
    statusBar()->showMessage(QString("缩放比例: %1%").arg(scaleFactor * 100, 0, 'f', 0));
}

void MainWindow::actualSize()
{
    if (!imageLoaded)
        return;

    scaleFactor = 1.0;
    displayImage();
    statusBar()->showMessage("显示实际大小");
}

void MainWindow::zoomIn()
{
    if (!imageLoaded)
        return;

    scaleImage(1.25);
}

void MainWindow::zoomOut()
{
    if (!imageLoaded)
        return;

    scaleImage(0.8);
}

// 帮助和关于对话框
void MainWindow::showAbout()
{
    QMessageBox aboutBox(this);
    aboutBox.setWindowTitle("关于本软件");
    // aboutBox.setWindowIcon(QIcon(":/icons/about.png"));

    QString aboutText = QString(
        "<h2>数字图像处理软件 V1.0</h2>"
        "<p><b>开发者：</b>丁浩君</p>"
        "<p><b>学号：</b>249400209</p>"
        "<p><b>功能特性：</b></p>"
        "<ul>"
        "<li>支持多种图片格式的打开和保存</li>"
        "<li>灰度化处理（(R+G+B)/3算法）</li>"
        "<li>二值化处理（可调阈值）</li>"
        "<li>3×3均值滤波</li>"
        "<li>伽马变换（针对彩色图像）</li>"
        "<li>Sobel边缘检测</li>"
        "<li>图像缩放和查看功能</li>"
        "</ul>"
        "<p><b>版权所有：</b>丁浩君图像处理工作室</p>"
        "<p><b>版本：</b>1.0</p>");

    aboutBox.setText(aboutText);
    aboutBox.setStandardButtons(QMessageBox::Ok);
    aboutBox.exec();
}

// 图像显示和缩放相关函数
void MainWindow::displayImage()
{
    if (!imageLoaded)
        return;

    QPixmap pixmap = QPixmap::fromImage(currentImage);
    if (scaleFactor != 1.0)
    {
        pixmap = pixmap.scaled(currentImage.size() * scaleFactor,
                               Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }

    ui->imageLabel->setPixmap(pixmap);
    ui->imageLabel->resize(pixmap.size());
}

void MainWindow::updateImageDisplay()
{
    bool hasImage = imageLoaded;

    // 启用/禁用菜单和工具栏动作
    saveAct->setEnabled(hasImage);
    grayscaleAct->setEnabled(hasImage);
    binaryAct->setEnabled(hasImage);
    meanFilterAct->setEnabled(hasImage);
    gammaAct->setEnabled(hasImage);
    edgeDetectionAct->setEnabled(hasImage);
    resetAct->setEnabled(hasImage);
    fitToWindowAct->setEnabled(hasImage);
    actualSizeAct->setEnabled(hasImage);
    zoomInAct->setEnabled(hasImage);
    zoomOutAct->setEnabled(hasImage);

    // 启用/禁用工具按钮
    ui->grayscaleBtn->setEnabled(hasImage);
    ui->binaryBtn->setEnabled(hasImage);
    ui->meanFilterBtn->setEnabled(hasImage);
    ui->gammaBtn->setEnabled(hasImage);
    ui->edgeDetectionBtn->setEnabled(hasImage);
    ui->resetBtn->setEnabled(hasImage);

    // 启用/禁用控制面板
    ui->thresholdSlider->setEnabled(hasImage);
    ui->thresholdSpinBox->setEnabled(hasImage);
    ui->gammaSlider->setEnabled(hasImage);
    ui->binaryGroupBox->setEnabled(hasImage);
    ui->gammaGroupBox->setEnabled(hasImage);

    if (!hasImage)
    {
        ui->imageLabel->setText("请打开图片文件");
        ui->imageLabel->setAlignment(Qt::AlignCenter);
        scaleFactor = 1.0;
    }
}

void MainWindow::scaleImage(double factor)
{
    scaleFactor *= factor;
    displayImage();

    statusBar()->showMessage(QString("缩放比例: %1%").arg(scaleFactor * 100, 0, 'f', 0));
}

// 图像处理算法实现
QImage MainWindow::processGrayscale(const QImage &image)
{
    QImage result = image.convertToFormat(QImage::Format_RGB32);

    for (int y = 0; y < result.height(); ++y)
    {
        QRgb *line = (QRgb *)result.scanLine(y);
        for (int x = 0; x < result.width(); ++x)
        {
            QRgb pixel = line[x];
            // 使用要求的公式：(R+G+B)/3
            int gray = (qRed(pixel) + qGreen(pixel) + qBlue(pixel)) / 3;
            line[x] = qRgb(gray, gray, gray);
        }
    }

    return result;
}

QImage MainWindow::processBinary(const QImage &image, int threshold)
{
    QImage grayImage = processGrayscale(image);
    QImage result = grayImage.convertToFormat(QImage::Format_RGB32);

    for (int y = 0; y < result.height(); ++y)
    {
        QRgb *line = (QRgb *)result.scanLine(y);
        for (int x = 0; x < result.width(); ++x)
        {
            QRgb pixel = line[x];
            int gray = qRed(pixel); // 已经是灰度图，R=G=B
            int binary = (gray >= threshold) ? 255 : 0;
            line[x] = qRgb(binary, binary, binary);
        }
    }

    return result;
}

QImage MainWindow::processMeanFilter(const QImage &image)
{
    QImage result = image.convertToFormat(QImage::Format_RGB32);
    QImage source = result; // 保存原始图像用于计算

    // 3x3均值滤波核
    for (int y = 1; y < result.height() - 1; ++y)
    {
        QRgb *line = (QRgb *)result.scanLine(y);
        for (int x = 1; x < result.width() - 1; ++x)
        {
            int sumR = 0, sumG = 0, sumB = 0;

            // 遍历3x3邻域
            for (int dy = -1; dy <= 1; ++dy)
            {
                QRgb *sourceLine = (QRgb *)source.scanLine(y + dy);
                for (int dx = -1; dx <= 1; ++dx)
                {
                    QRgb pixel = sourceLine[x + dx];
                    sumR += qRed(pixel);
                    sumG += qGreen(pixel);
                    sumB += qBlue(pixel);
                }
            }

            // 计算平均值
            int avgR = sumR / 9;
            int avgG = sumG / 9;
            int avgB = sumB / 9;

            line[x] = qRgb(avgR, avgG, avgB);
        }
    }

    return result;
}

QImage MainWindow::processGammaCorrection(const QImage &image, double gamma)
{
    QImage result = image.convertToFormat(QImage::Format_RGB32);

    // 预计算伽马查找表以提高性能
    int gammaTable[256];
    for (int i = 0; i < 256; ++i)
    {
        double normalized = i / 255.0;
        double corrected = pow(normalized, 1.0 / gamma);
        gammaTable[i] = qBound(0, (int)(corrected * 255), 255);
    }

    for (int y = 0; y < result.height(); ++y)
    {
        QRgb *line = (QRgb *)result.scanLine(y);
        for (int x = 0; x < result.width(); ++x)
        {
            QRgb pixel = line[x];
            int r = gammaTable[qRed(pixel)];
            int g = gammaTable[qGreen(pixel)];
            int b = gammaTable[qBlue(pixel)];
            line[x] = qRgb(r, g, b);
        }
    }

    return result;
}

QImage MainWindow::processEdgeDetection(const QImage &image)
{
    // 先转换为灰度图
    QImage grayImage = processGrayscale(image);
    QImage result = grayImage.convertToFormat(QImage::Format_RGB32);

    // Sobel算子
    int sobelX[3][3] = {{-1, 0, 1}, {-2, 0, 2}, {-1, 0, 1}};
    int sobelY[3][3] = {{-1, -2, -1}, {0, 0, 0}, {1, 2, 1}};

    for (int y = 1; y < result.height() - 1; ++y)
    {
        QRgb *line = (QRgb *)result.scanLine(y);
        for (int x = 1; x < result.width() - 1; ++x)
        {
            int gx = 0, gy = 0;

            // 应用Sobel算子
            for (int dy = -1; dy <= 1; ++dy)
            {
                QRgb *sourceLine = (QRgb *)grayImage.scanLine(y + dy);
                for (int dx = -1; dx <= 1; ++dx)
                {
                    QRgb pixel = sourceLine[x + dx];
                    int gray = qRed(pixel); // 灰度值

                    gx += gray * sobelX[dy + 1][dx + 1];
                    gy += gray * sobelY[dy + 1][dx + 1];
                }
            }

            // 计算梯度幅值
            int magnitude = qBound(0, (int)sqrt(gx * gx + gy * gy), 255);
            line[x] = qRgb(magnitude, magnitude, magnitude);
        }
    }

    return result;
}