# 数字图像处理软件使用说明

## 软件信息
- **软件名称**：数字图像处理软件V1.0
- **开发者**：丁浩君
- **学号**：249400209
- **工程名**：ImageProcessor_DingHaoJun_249400209

## 快速开始

### 1. 启动软件
双击运行 `ImageProcessor_DingHaoJun_249400209.exe`

### 2. 打开图片
- 方法一：点击菜单栏"文件" → "打开图片"
- 方法二：使用快捷键 `Ctrl+O`
- 方法三：点击工具栏的打开按钮

### 3. 图像处理操作

#### 灰度化处理
- 菜单路径：编辑 → 灰度化
- 快捷键：`Ctrl+G`
- 功能：将彩色图片转换为灰度图片
- 算法：使用(R+G+B)/3公式

#### 二值化处理
- 菜单路径：编辑 → 二值化
- 快捷键：`Ctrl+B`
- 功能：将图片转换为黑白二值图
- 参数调整：使用右侧控制面板的阈值滑条（0-255）

#### 3×3均值滤波
- 菜单路径：编辑 → 均值滤波
- 快捷键：`Ctrl+F`
- 功能：平滑图像，减少噪声

#### 伽马变换
- 菜单路径：编辑 → 伽马变换
- 快捷键：`Ctrl+A`
- 功能：调整图像亮度和对比度
- 参数调整：使用右侧控制面板的伽马滑条（0.1-3.0）

#### 边缘检测
- 菜单路径：编辑 → 边缘检测
- 快捷键：`Ctrl+E`
- 功能：使用Sobel算子检测图像边缘

### 4. 视图控制

#### 缩放功能
- **适应窗口**：`Ctrl+W` - 自动缩放图片适应窗口大小
- **实际大小**：`Ctrl+1` - 显示图片原始大小
- **放大**：`Ctrl++` - 放大图片
- **缩小**：`Ctrl+-` - 缩小图片

#### 重置功能
- **重置图片**：`Ctrl+R` - 恢复到原始图片状态

### 5. 保存图片
- 菜单路径：文件 → 保存图片
- 快捷键：`Ctrl+S`
- 支持格式：PNG、JPEG、BMP

## 界面说明

### 主界面布局
```
┌─────────────────────────────────────────────────────┐
│ 菜单栏：文件 编辑 视图 帮助                           │
├─────────────────────────────────────────────────────┤
│ 工具栏：[打开] [保存] [退出] | [灰度] [二值] [滤波]... │
├─────────────────────────────────────────────────────┤
│ 图像显示区域                    │ 工具面板            │
│                               │ ┌─────────────────┐ │
│                               │ │ 图像处理工具    │ │
│                               │ │ [灰度][二值]    │ │
│                               │ │ [滤波][伽马]    │ │
│                               │ │ [边缘][重置]    │ │
│                               │ └─────────────────┘ │
│                               │ ┌─────────────────┐ │
│                               │ │ 二值化阈值      │ │
│                               │ │ [滑条] [数值框] │ │
│                               │ └─────────────────┘ │
│                               │ ┌─────────────────┐ │
│                               │ │ 伽马变换        │ │
│                               │ │ [滑条] [显示值] │ │
│                               │ └─────────────────┘ │
├─────────────────────────────────────────────────────┤
│ 状态栏：显示操作信息和图片信息                        │
└─────────────────────────────────────────────────────┘
```

### 工具面板
- **位置**：窗口右侧，画图板风格布局
- **工具按钮**：
  - 2×3网格布局，每个按钮50×50像素
  - 包含：灰度、二值、滤波、伽马、边缘、重置
  - 鼠标悬停和点击效果
  - 支持快捷键操作
- **二值化阈值控制**：
  - 滑条范围：0-255
  - 默认值：128
  - 实时预览效果
- **伽马变换控制**：
  - 滑条范围：0.1-3.0
  - 默认值：1.0
  - 实时显示当前伽马值

## 快捷键一览表

| 功能 | 快捷键 |
|------|--------|
| 打开图片 | Ctrl+O |
| 打开多张图片 | Ctrl+Shift+O |
| 保存图片 | Ctrl+S |
| 退出程序 | Ctrl+Q |
| 灰度化 | Ctrl+G |
| 二值化 | Ctrl+B |
| 均值滤波 | Ctrl+F |
| 伽马变换 | Ctrl+A |
| 边缘检测 | Ctrl+E |
| 重置图片 | Ctrl+R |
| 适应窗口 | Ctrl+W |
| 实际大小 | Ctrl+1 |
| 放大 | Ctrl++ |
| 缩小 | Ctrl+- |

## 支持的图片格式

### 输入格式
- PNG (.png)
- JPEG (.jpg, .jpeg)
- BMP (.bmp)
- GIF (.gif)
- TIFF (.tiff)

### 输出格式
- PNG (.png)
- JPEG (.jpg)
- BMP (.bmp)

## 注意事项

1. **图片加载**：确保图片文件未被其他程序占用
2. **内存使用**：处理大尺寸图片时可能需要较多内存
3. **处理顺序**：建议按需要的顺序依次应用处理效果
4. **参数调整**：二值化和伽马变换支持实时参数调整
5. **保存提醒**：处理后记得保存图片，否则关闭软件后会丢失

## 常见问题

**Q: 为什么有些菜单项是灰色的？**
A: 需要先加载图片，图像处理功能才会激活。

**Q: 如何恢复原始图片？**
A: 使用"重置图片"功能（Ctrl+R）或重新打开图片。

**Q: 处理后的图片质量如何？**
A: 软件使用高质量的图像处理算法，保证处理效果。

**Q: 可以同时打开多张图片吗？**
A: 支持多选打开，但当前只显示第一张图片。

## 技术支持

如有问题，请联系：
- 开发者：丁浩君
- 学号：249400209
