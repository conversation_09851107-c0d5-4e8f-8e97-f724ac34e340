#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QApplication>
#include <QScreen>
#include <QMimeData>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QUrl>
#include <QTextStream>
#include <QDebug>
#include <cmath>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::MainWindow), scaleFactor(1.0), imageLoaded(false)
{
    ui->setupUi(this);

    // 设置窗口标题 - 请根据实际姓名和学号修改
    setWindowTitle("数字图像处理软件V1.0 丁浩君 249400209");

    // 设置窗口图标（暂时注释，需要添加实际图标文件）
    // setWindowIcon(QIcon(":/icons/app_icon.ico"));

    // 初始化视频处理相关变量
    isVideoLoaded = false;
    isVideoPlaying = false;
    isProcessingVideo = false;
    currentFrame = 0;
    totalFrames = 0;
    videoFPS = 30.0;
    currentVideoMode = "原始";

    // 创建视频相关对象 - 纯Qt实现
    videoTimer = new QTimer(this);
    frameProcessingTimer = new QTimer(this);
    mediaPlayer = new QMediaPlayer(this);
    videoWidget = new QVideoWidget();

    // 初始化视频播放器
    mediaPlayer->setVideoOutput(videoWidget);
    videoWidget->hide(); // 默认隐藏，需要时显示

    // 初始化UI
    setupUI();
    createMenus();
    createToolBars();
    createStatusBar();
    connectSignals();

    // 启用拖放
    setAcceptDrops(true);

    // 初始状态
    updateImageDisplay();
}

MainWindow::~MainWindow()
{
    delete ui;
}

// UI设置函数
void MainWindow::setupUI()
{
    // UI文件已经设置了界面布局，这里只需要做一些额外的配置
    ui->imageLabel->setScaledContents(true);
    ui->imageScrollArea->setBackgroundRole(QPalette::Dark);
}

void MainWindow::createMenus()
{
    // 文件菜单
    fileMenu = menuBar()->addMenu("文件(&F)");

    openAct = new QAction("打开图片(&O)", this);
    openAct->setShortcuts(QKeySequence::Open);
    openAct->setStatusTip("打开一张图片");
    fileMenu->addAction(openAct);

    openMultipleAct = new QAction("打开多张图片(&M)", this);
    openMultipleAct->setShortcut(QKeySequence("Ctrl+Shift+O"));
    openMultipleAct->setStatusTip("打开多张图片");
    fileMenu->addAction(openMultipleAct);

    fileMenu->addSeparator();

    saveAct = new QAction("保存图片(&S)", this);
    saveAct->setShortcuts(QKeySequence::Save);
    saveAct->setStatusTip("保存当前图片");
    saveAct->setEnabled(false);
    fileMenu->addAction(saveAct);

    fileMenu->addSeparator();

    exitAct = new QAction("退出(&X)", this);
    exitAct->setShortcuts(QKeySequence::Quit);
    exitAct->setStatusTip("退出应用程序");
    fileMenu->addAction(exitAct);

    // 编辑菜单
    editMenu = menuBar()->addMenu("编辑(&E)");

    grayscaleAct = new QAction("灰度化(&G)", this);
    grayscaleAct->setShortcut(QKeySequence("Ctrl+G"));
    grayscaleAct->setStatusTip("将图片转换为灰度图");
    grayscaleAct->setEnabled(false);
    editMenu->addAction(grayscaleAct);

    binaryAct = new QAction("二值化(&B)", this);
    binaryAct->setShortcut(QKeySequence("Ctrl+B"));
    binaryAct->setStatusTip("将图片转换为二值图");
    binaryAct->setEnabled(false);
    editMenu->addAction(binaryAct);

    meanFilterAct = new QAction("均值滤波(&F)", this);
    meanFilterAct->setShortcut(QKeySequence("Ctrl+F"));
    meanFilterAct->setStatusTip("应用3x3均值滤波");
    meanFilterAct->setEnabled(false);
    editMenu->addAction(meanFilterAct);

    gammaAct = new QAction("伽马变换(&A)", this);
    gammaAct->setShortcut(QKeySequence("Ctrl+A"));
    gammaAct->setStatusTip("应用伽马变换");
    gammaAct->setEnabled(false);
    editMenu->addAction(gammaAct);

    edgeDetectionAct = new QAction("边缘检测(&E)", this);
    edgeDetectionAct->setShortcut(QKeySequence("Ctrl+E"));
    edgeDetectionAct->setStatusTip("Sobel边缘检测");
    edgeDetectionAct->setEnabled(false);
    editMenu->addAction(edgeDetectionAct);

    editMenu->addSeparator();

    resetAct = new QAction("重置图片(&R)", this);
    resetAct->setShortcut(QKeySequence("Ctrl+R"));
    resetAct->setStatusTip("重置到原始图片");
    resetAct->setEnabled(false);
    editMenu->addAction(resetAct);

    // 视图菜单
    viewMenu = menuBar()->addMenu("视图(&V)");

    fitToWindowAct = new QAction("适应窗口(&W)", this);
    fitToWindowAct->setShortcut(QKeySequence("Ctrl+W"));
    fitToWindowAct->setStatusTip("缩放图片以适应窗口");
    fitToWindowAct->setEnabled(false);
    viewMenu->addAction(fitToWindowAct);

    actualSizeAct = new QAction("实际大小(&1)", this);
    actualSizeAct->setShortcut(QKeySequence("Ctrl+1"));
    actualSizeAct->setStatusTip("显示图片实际大小");
    actualSizeAct->setEnabled(false);
    viewMenu->addAction(actualSizeAct);

    viewMenu->addSeparator();

    zoomInAct = new QAction("放大(&+)", this);
    zoomInAct->setShortcut(QKeySequence::ZoomIn);
    zoomInAct->setStatusTip("放大图片");
    zoomInAct->setEnabled(false);
    viewMenu->addAction(zoomInAct);

    zoomOutAct = new QAction("缩小(&-)", this);
    zoomOutAct->setShortcut(QKeySequence::ZoomOut);
    zoomOutAct->setStatusTip("缩小图片");
    zoomOutAct->setEnabled(false);
    viewMenu->addAction(zoomOutAct);

    // 视频菜单 (B档功能)
    videoMenu = menuBar()->addMenu("视频(&V)");

    openVideoAct = new QAction("打开视频文件(&O)", this);
    openVideoAct->setShortcut(QKeySequence("Ctrl+Shift+V"));
    openVideoAct->setStatusTip("打开视频文件进行处理");
    videoMenu->addAction(openVideoAct);

    openCameraAct = new QAction("打开摄像头(&C)", this);
    openCameraAct->setShortcut(QKeySequence("Ctrl+Shift+C"));
    openCameraAct->setStatusTip("打开摄像头进行实时处理");
    videoMenu->addAction(openCameraAct);

    videoMenu->addSeparator();

    startVideoAct = new QAction("开始处理(&S)", this);
    startVideoAct->setShortcut(QKeySequence("Ctrl+Shift+S"));
    startVideoAct->setStatusTip("开始视频处理");
    startVideoAct->setEnabled(false);
    videoMenu->addAction(startVideoAct);

    pauseVideoAct = new QAction("暂停处理(&P)", this);
    pauseVideoAct->setShortcut(QKeySequence("Ctrl+Shift+P"));
    pauseVideoAct->setStatusTip("暂停视频处理");
    pauseVideoAct->setEnabled(false);
    videoMenu->addAction(pauseVideoAct);

    stopVideoAct = new QAction("停止处理(&T)", this);
    stopVideoAct->setShortcut(QKeySequence("Ctrl+Shift+T"));
    stopVideoAct->setStatusTip("停止视频处理");
    stopVideoAct->setEnabled(false);
    videoMenu->addAction(stopVideoAct);

    // 帮助菜单
    helpMenu = menuBar()->addMenu("帮助(&H)");

    aboutAct = new QAction("关于(&A)", this);
    aboutAct->setStatusTip("关于本软件");
    helpMenu->addAction(aboutAct);
}

void MainWindow::createToolBars()
{
    // 文件工具栏
    fileToolBar = addToolBar("文件");
    fileToolBar->addAction(openAct);
    fileToolBar->addAction(saveAct);
    fileToolBar->addSeparator();
    fileToolBar->addAction(exitAct);

    // 编辑工具栏
    editToolBar = addToolBar("图像处理");
    editToolBar->addAction(grayscaleAct);
    editToolBar->addAction(binaryAct);
    editToolBar->addAction(meanFilterAct);
    editToolBar->addAction(gammaAct);
    editToolBar->addAction(edgeDetectionAct);
    editToolBar->addSeparator();
    editToolBar->addAction(resetAct);

    // 视图工具栏
    viewToolBar = addToolBar("视图");
    viewToolBar->addAction(fitToWindowAct);
    viewToolBar->addAction(actualSizeAct);
    viewToolBar->addSeparator();
    viewToolBar->addAction(zoomInAct);
    viewToolBar->addAction(zoomOutAct);
}

void MainWindow::createStatusBar()
{
    statusBar()->showMessage("就绪");
}

void MainWindow::connectSignals()
{
    // 文件操作信号连接
    connect(openAct, &QAction::triggered, this, &MainWindow::openImage);
    connect(openMultipleAct, &QAction::triggered, this, &MainWindow::openMultipleImages);
    connect(saveAct, &QAction::triggered, this, &MainWindow::saveImage);
    connect(exitAct, &QAction::triggered, this, &MainWindow::exitApplication);

    // 图像处理信号连接（菜单）
    connect(grayscaleAct, &QAction::triggered, this, &MainWindow::convertToGrayscale);
    connect(binaryAct, &QAction::triggered, this, &MainWindow::convertToBinary);
    connect(meanFilterAct, &QAction::triggered, this, &MainWindow::applyMeanFilter);
    connect(gammaAct, &QAction::triggered, this, &MainWindow::applyGammaCorrection);
    connect(edgeDetectionAct, &QAction::triggered, this, &MainWindow::detectEdges);
    connect(resetAct, &QAction::triggered, this, &MainWindow::resetImage);

    // 图像处理信号连接（工具按钮）
    connect(ui->grayscaleBtn, &QPushButton::clicked, this, &MainWindow::convertToGrayscale);
    connect(ui->binaryBtn, &QPushButton::clicked, this, &MainWindow::convertToBinary);
    connect(ui->meanFilterBtn, &QPushButton::clicked, this, &MainWindow::applyMeanFilter);
    connect(ui->gammaBtn, &QPushButton::clicked, this, &MainWindow::applyGammaCorrection);
    connect(ui->edgeDetectionBtn, &QPushButton::clicked, this, &MainWindow::detectEdges);
    connect(ui->resetBtn, &QPushButton::clicked, this, &MainWindow::resetImage);

    // 视图控制信号连接
    connect(fitToWindowAct, &QAction::triggered, this, &MainWindow::fitToWindow);
    connect(actualSizeAct, &QAction::triggered, this, &MainWindow::actualSize);
    connect(zoomInAct, &QAction::triggered, this, &MainWindow::zoomIn);
    connect(zoomOutAct, &QAction::triggered, this, &MainWindow::zoomOut);

    // 帮助信号连接
    connect(aboutAct, &QAction::triggered, this, &MainWindow::showAbout);

    // 控制面板信号连接
    connect(ui->thresholdSlider, &QSlider::valueChanged, this, &MainWindow::onThresholdChanged);
    connect(ui->thresholdSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            ui->thresholdSlider, &QSlider::setValue);
    connect(ui->thresholdSlider, &QSlider::valueChanged,
            ui->thresholdSpinBox, &QSpinBox::setValue);

    connect(ui->gammaSlider, &QSlider::valueChanged, this, [this](int value)
            {
        double gamma = value / 100.0;
        ui->gammaValueLabel->setText(QString("伽马值: %1").arg(gamma, 0, 'f', 1));
        onGammaChanged(gamma); });

    // 视频处理信号连接
    connect(ui->openVideoBtn, &QPushButton::clicked, this, &MainWindow::openVideo);
    connect(ui->openCameraBtn, &QPushButton::clicked, this, &MainWindow::openCamera);
    connect(ui->startVideoBtn, &QPushButton::clicked, this, &MainWindow::startVideoProcessing);
    connect(ui->pauseVideoBtn, &QPushButton::clicked, this, &MainWindow::pauseVideoProcessing);
    connect(ui->stopVideoBtn, &QPushButton::clicked, this, &MainWindow::stopVideoProcessing);

    // 视频菜单信号连接
    connect(openVideoAct, &QAction::triggered, this, &MainWindow::openVideo);
    connect(openCameraAct, &QAction::triggered, this, &MainWindow::openCamera);
    connect(startVideoAct, &QAction::triggered, this, &MainWindow::startVideoProcessing);
    connect(pauseVideoAct, &QAction::triggered, this, &MainWindow::pauseVideoProcessing);
    connect(stopVideoAct, &QAction::triggered, this, &MainWindow::stopVideoProcessing);

    // 视频控制信号连接
    connect(ui->videoModeCombo, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &MainWindow::onVideoProcessingModeChanged);
    connect(ui->videoIntervalSlider, &QSlider::valueChanged, this, &MainWindow::onVideoIntervalChanged);
    connect(videoTimer, &QTimer::timeout, this, &MainWindow::onVideoTimer);

    // Qt多媒体信号连接
    connect(mediaPlayer, &QMediaPlayer::mediaStatusChanged, this, &MainWindow::onMediaStatusChanged);
    connect(mediaPlayer, &QMediaPlayer::positionChanged, this, &MainWindow::onPositionChanged);
    connect(mediaPlayer, &QMediaPlayer::durationChanged, this, &MainWindow::onDurationChanged);
    connect(frameProcessingTimer, &QTimer::timeout, this, &MainWindow::onVideoFrameChanged);
}

// 文件操作槽函数实现
void MainWindow::openImage()
{
    QString fileName = QFileDialog::getOpenFileName(this,
                                                    "打开图片", QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
                                                    "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.tiff)");

    if (!fileName.isEmpty())
    {
        QImage image(fileName);
        if (image.isNull())
        {
            showCustomMessageBox("错误 - 丁浩君 249400209",
                                 QString("无法加载图片 %1\n请检查文件格式是否支持。").arg(QFileInfo(fileName).fileName()),
                                 QMessageBox::Critical);
            return;
        }

        originalImage = image;
        currentImage = image;
        imageLoaded = true;

        displayImage();
        updateImageDisplay();

        statusBar()->showMessage(QString("已加载图片: %1 (%2x%3)")
                                     .arg(QFileInfo(fileName).fileName())
                                     .arg(image.width())
                                     .arg(image.height()));
    }
}

void MainWindow::openMultipleImages()
{
    QStringList fileNames = QFileDialog::getOpenFileNames(this,
                                                          "打开多张图片", QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
                                                          "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.tiff)");

    if (!fileNames.isEmpty())
    {
        // 显示第一张图片
        QString firstName = fileNames.first();
        QImage image(firstName);
        if (image.isNull())
        {
            QMessageBox::information(this, "图像处理软件",
                                     "无法加载图片 " + firstName + "。");
            return;
        }

        originalImage = image;
        currentImage = image;
        imageLoaded = true;

        displayImage();
        updateImageDisplay();

        statusBar()->showMessage(QString("已加载 %1 张图片，当前显示: %2 (%3x%4)")
                                     .arg(fileNames.size())
                                     .arg(QFileInfo(firstName).fileName())
                                     .arg(image.width())
                                     .arg(image.height()));

        // 显示加载的文件列表
        QString fileList = "已加载的图片文件:\n";
        for (const QString &fileName : fileNames)
        {
            fileList += QFileInfo(fileName).fileName() + "\n";
        }
        QMessageBox::information(this, "多文件加载", fileList);
    }
}

void MainWindow::saveImage()
{
    if (!imageLoaded)
    {
        QMessageBox::information(this, "图像处理软件", "没有图片可以保存。");
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this,
                                                    "保存图片", QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
                                                    "PNG文件 (*.png);;JPEG文件 (*.jpg);;BMP文件 (*.bmp)");

    if (!fileName.isEmpty())
    {
        if (currentImage.save(fileName))
        {
            statusBar()->showMessage("图片已保存: " + QFileInfo(fileName).fileName());
        }
        else
        {
            QMessageBox::information(this, "图像处理软件", "保存图片失败。");
        }
    }
}

void MainWindow::exitApplication()
{
    QApplication::quit();
}

// 图像处理槽函数实现
void MainWindow::convertToGrayscale()
{
    if (!imageLoaded)
    {
        showCustomMessageBox("提示 - 丁浩君 249400209", "请先加载图片。", QMessageBox::Information);
        return;
    }

    currentImage = processGrayscale(originalImage);
    displayImage();
    statusBar()->showMessage("已应用灰度化处理");
    showCustomMessageBox("处理完成 - 丁浩君 249400209", "灰度化处理已完成！\n使用算法：(R+G+B)/3", QMessageBox::Information);
}

void MainWindow::convertToBinary()
{
    if (!imageLoaded)
    {
        showCustomMessageBox("提示 - 丁浩君 249400209", "请先加载图片。", QMessageBox::Information);
        return;
    }

    int threshold = ui->thresholdSlider->value();
    currentImage = processBinary(originalImage, threshold);
    displayImage();
    statusBar()->showMessage(QString("已应用二值化处理，阈值: %1").arg(threshold));
    showCustomMessageBox("处理完成 - 丁浩君 249400209",
                         QString("二值化处理已完成！\n阈值设置：%1\n像素值大于等于阈值显示为白色，小于阈值显示为黑色。").arg(threshold),
                         QMessageBox::Information);
}

void MainWindow::applyMeanFilter()
{
    if (!imageLoaded)
    {
        showCustomMessageBox("提示 - 丁浩君 249400209", "请先加载图片。", QMessageBox::Information);
        return;
    }

    currentImage = processMeanFilter(originalImage);
    displayImage();
    statusBar()->showMessage("已应用3x3均值滤波");
    showCustomMessageBox("处理完成 - 丁浩君 249400209",
                         "3×3均值滤波处理已完成！\n该滤波器可以平滑图像，减少噪声。",
                         QMessageBox::Information);
}

void MainWindow::applyGammaCorrection()
{
    if (!imageLoaded)
    {
        showCustomMessageBox("提示 - 丁浩君 249400209", "请先加载图片。", QMessageBox::Information);
        return;
    }

    double gamma = ui->gammaSlider->value() / 100.0;
    currentImage = processGammaCorrection(originalImage, gamma);
    displayImage();
    statusBar()->showMessage(QString("已应用伽马变换，伽马值: %1").arg(gamma, 0, 'f', 1));
    showCustomMessageBox("处理完成 - 丁浩君 249400209",
                         QString("伽马变换处理已完成！\n伽马值：%1\n该变换可以调整图像的亮度和对比度。").arg(gamma, 0, 'f', 1),
                         QMessageBox::Information);
}

void MainWindow::detectEdges()
{
    if (!imageLoaded)
    {
        showCustomMessageBox("提示 - 丁浩君 249400209", "请先加载图片。", QMessageBox::Information);
        return;
    }

    currentImage = processEdgeDetection(originalImage);
    displayImage();
    statusBar()->showMessage("已应用Sobel边缘检测");
    showCustomMessageBox("处理完成 - 丁浩君 249400209",
                         "Sobel边缘检测处理已完成！\n该算法可以检测图像中的边缘特征。",
                         QMessageBox::Information);
}

// 界面控制槽函数实现
void MainWindow::onThresholdChanged(int value)
{
    if (imageLoaded)
    {
        currentImage = processBinary(originalImage, value);
        displayImage();
        statusBar()->showMessage(QString("二值化阈值: %1").arg(value));
    }
}

void MainWindow::onGammaChanged(double value)
{
    if (imageLoaded)
    {
        currentImage = processGammaCorrection(originalImage, value);
        displayImage();
        statusBar()->showMessage(QString("伽马值: %1").arg(value, 0, 'f', 1));
    }
}

void MainWindow::resetImage()
{
    if (!imageLoaded)
    {
        QMessageBox::information(this, "图像处理软件", "请先加载图片。");
        return;
    }

    currentImage = originalImage;
    displayImage();
    statusBar()->showMessage("已重置到原始图片");
}

void MainWindow::fitToWindow()
{
    if (!imageLoaded)
        return;

    QSize scrollAreaSize = ui->imageScrollArea->size();
    QSize imageSize = currentImage.size();

    double scaleX = (double)scrollAreaSize.width() / imageSize.width();
    double scaleY = (double)scrollAreaSize.height() / imageSize.height();
    scaleFactor = qMin(scaleX, scaleY) * 0.9; // 留一些边距

    displayImage();
    statusBar()->showMessage(QString("缩放比例: %1%").arg(scaleFactor * 100, 0, 'f', 0));
}

void MainWindow::actualSize()
{
    if (!imageLoaded)
        return;

    scaleFactor = 1.0;
    displayImage();
    statusBar()->showMessage("显示实际大小");
}

void MainWindow::zoomIn()
{
    if (!imageLoaded)
        return;

    scaleImage(1.25);
}

void MainWindow::zoomOut()
{
    if (!imageLoaded)
        return;

    scaleImage(0.8);
}

// 帮助和关于对话框
void MainWindow::showAbout()
{
    QMessageBox aboutBox(this);
    aboutBox.setWindowTitle("关于本软件 - 丁浩君 249400209");

    // 设置自定义图标（关于.png）
    QPixmap aboutIcon(":/icons/关于.png");
    if (!aboutIcon.isNull())
    {
        aboutBox.setIconPixmap(aboutIcon.scaled(64, 64, Qt::KeepAspectRatio, Qt::SmoothTransformation));
        aboutBox.setWindowIcon(QIcon(":/icons/关于.png"));
    }
    else
    {
        // 如果自定义图标不存在，使用系统默认信息图标
        aboutBox.setIcon(QMessageBox::Information);
    }

    QString aboutText = QString(
        "<h2>数字图像处理软件 V1.0</h2>"
        "<p><b>开发者：</b>丁浩君</p>"
        "<p><b>学号：</b>249400209</p>"
        "<p><b>课程：</b>数字图像处理</p>"
        "<p><b>功能特性：</b></p>"
        "<ul>"
        "<li>支持多种图片格式的打开和保存</li>"
        "<li>灰度化处理（(R+G+B)/3算法）</li>"
        "<li>二值化处理（可调阈值滑条）</li>"
        "<li>3×3均值滤波</li>"
        "<li>伽马变换（针对彩色图像）</li>"
        "<li>Sobel边缘检测</li>"
        "<li>图像缩放和查看功能</li>"
        "<li>画图板风格工具面板</li>"
        "</ul>"
        "<p><b>版权所有：</b>丁浩君图像处理工作室</p>"
        "<p><b>版本：</b>1.0</p>"
        "<p><b>开发时间：</b>2024年</p>");

    aboutBox.setText(aboutText);
    aboutBox.setStandardButtons(QMessageBox::Ok);
    aboutBox.exec();
}

// 自定义消息框函数
void MainWindow::showCustomMessageBox(const QString &title, const QString &text, QMessageBox::Icon icon)
{
    QMessageBox msgBox(this);
    msgBox.setWindowTitle(title);
    msgBox.setText(text);

    // 设置自定义图标（关于.png）
    QPixmap customIcon(":/icons/关于.png");
    if (!customIcon.isNull())
    {
        // 根据消息类型调整图标大小和颜色
        QSize iconSize(48, 48);
        if (icon == QMessageBox::Warning)
        {
            iconSize = QSize(32, 32);
        }
        else if (icon == QMessageBox::Critical)
        {
            iconSize = QSize(32, 32);
        }
        msgBox.setIconPixmap(customIcon.scaled(iconSize, Qt::KeepAspectRatio, Qt::SmoothTransformation));
        msgBox.setWindowIcon(QIcon(":/icons/关于.png"));
    }
    else
    {
        // 如果自定义图标不存在，使用系统默认图标
        msgBox.setIcon(icon);
    }

    msgBox.setStandardButtons(QMessageBox::Ok);
    msgBox.exec();
}

// 图像显示和缩放相关函数
void MainWindow::displayImage()
{
    if (!imageLoaded)
        return;

    QPixmap pixmap = QPixmap::fromImage(currentImage);
    if (scaleFactor != 1.0)
    {
        pixmap = pixmap.scaled(currentImage.size() * scaleFactor,
                               Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }

    ui->imageLabel->setPixmap(pixmap);
    ui->imageLabel->resize(pixmap.size());
}

void MainWindow::updateImageDisplay()
{
    bool hasImage = imageLoaded;

    // 启用/禁用菜单和工具栏动作
    saveAct->setEnabled(hasImage);
    grayscaleAct->setEnabled(hasImage);
    binaryAct->setEnabled(hasImage);
    meanFilterAct->setEnabled(hasImage);
    gammaAct->setEnabled(hasImage);
    edgeDetectionAct->setEnabled(hasImage);
    resetAct->setEnabled(hasImage);
    fitToWindowAct->setEnabled(hasImage);
    actualSizeAct->setEnabled(hasImage);
    zoomInAct->setEnabled(hasImage);
    zoomOutAct->setEnabled(hasImage);

    // 启用/禁用工具按钮
    ui->grayscaleBtn->setEnabled(hasImage);
    ui->binaryBtn->setEnabled(hasImage);
    ui->meanFilterBtn->setEnabled(hasImage);
    ui->gammaBtn->setEnabled(hasImage);
    ui->edgeDetectionBtn->setEnabled(hasImage);
    ui->resetBtn->setEnabled(hasImage);

    // 启用/禁用控制面板
    ui->thresholdSlider->setEnabled(hasImage);
    ui->thresholdSpinBox->setEnabled(hasImage);
    ui->gammaSlider->setEnabled(hasImage);
    ui->binaryGroupBox->setEnabled(hasImage);
    ui->gammaGroupBox->setEnabled(hasImage);

    if (!hasImage)
    {
        ui->imageLabel->setText("请打开图片文件");
        ui->imageLabel->setAlignment(Qt::AlignCenter);
        scaleFactor = 1.0;
    }
}

void MainWindow::scaleImage(double factor)
{
    scaleFactor *= factor;
    displayImage();

    statusBar()->showMessage(QString("缩放比例: %1%").arg(scaleFactor * 100, 0, 'f', 0));
}

// 图像处理算法实现
QImage MainWindow::processGrayscale(const QImage &image)
{
    QImage result = image.convertToFormat(QImage::Format_RGB32);

    for (int y = 0; y < result.height(); ++y)
    {
        QRgb *line = (QRgb *)result.scanLine(y);
        for (int x = 0; x < result.width(); ++x)
        {
            QRgb pixel = line[x];
            // 使用要求的公式：(R+G+B)/3
            int gray = (qRed(pixel) + qGreen(pixel) + qBlue(pixel)) / 3;
            line[x] = qRgb(gray, gray, gray);
        }
    }

    return result;
}

QImage MainWindow::processBinary(const QImage &image, int threshold)
{
    QImage grayImage = processGrayscale(image);
    QImage result = grayImage.convertToFormat(QImage::Format_RGB32);

    for (int y = 0; y < result.height(); ++y)
    {
        QRgb *line = (QRgb *)result.scanLine(y);
        for (int x = 0; x < result.width(); ++x)
        {
            QRgb pixel = line[x];
            int gray = qRed(pixel); // 已经是灰度图，R=G=B
            int binary = (gray >= threshold) ? 255 : 0;
            line[x] = qRgb(binary, binary, binary);
        }
    }

    return result;
}

QImage MainWindow::processMeanFilter(const QImage &image)
{
    QImage result = image.convertToFormat(QImage::Format_RGB32);
    QImage source = result; // 保存原始图像用于计算

    // 3x3均值滤波核
    for (int y = 1; y < result.height() - 1; ++y)
    {
        QRgb *line = (QRgb *)result.scanLine(y);
        for (int x = 1; x < result.width() - 1; ++x)
        {
            int sumR = 0, sumG = 0, sumB = 0;

            // 遍历3x3邻域
            for (int dy = -1; dy <= 1; ++dy)
            {
                QRgb *sourceLine = (QRgb *)source.scanLine(y + dy);
                for (int dx = -1; dx <= 1; ++dx)
                {
                    QRgb pixel = sourceLine[x + dx];
                    sumR += qRed(pixel);
                    sumG += qGreen(pixel);
                    sumB += qBlue(pixel);
                }
            }

            // 计算平均值
            int avgR = sumR / 9;
            int avgG = sumG / 9;
            int avgB = sumB / 9;

            line[x] = qRgb(avgR, avgG, avgB);
        }
    }

    return result;
}

QImage MainWindow::processGammaCorrection(const QImage &image, double gamma)
{
    QImage result = image.convertToFormat(QImage::Format_RGB32);

    // 预计算伽马查找表以提高性能
    int gammaTable[256];
    for (int i = 0; i < 256; ++i)
    {
        double normalized = i / 255.0;
        double corrected = pow(normalized, 1.0 / gamma);
        gammaTable[i] = qBound(0, (int)(corrected * 255), 255);
    }

    for (int y = 0; y < result.height(); ++y)
    {
        QRgb *line = (QRgb *)result.scanLine(y);
        for (int x = 0; x < result.width(); ++x)
        {
            QRgb pixel = line[x];
            int r = gammaTable[qRed(pixel)];
            int g = gammaTable[qGreen(pixel)];
            int b = gammaTable[qBlue(pixel)];
            line[x] = qRgb(r, g, b);
        }
    }

    return result;
}

QImage MainWindow::processEdgeDetection(const QImage &image)
{
    // 先转换为灰度图
    QImage grayImage = processGrayscale(image);
    QImage result = grayImage.convertToFormat(QImage::Format_RGB32);

    // Sobel算子
    int sobelX[3][3] = {{-1, 0, 1}, {-2, 0, 2}, {-1, 0, 1}};
    int sobelY[3][3] = {{-1, -2, -1}, {0, 0, 0}, {1, 2, 1}};

    for (int y = 1; y < result.height() - 1; ++y)
    {
        QRgb *line = (QRgb *)result.scanLine(y);
        for (int x = 1; x < result.width() - 1; ++x)
        {
            int gx = 0, gy = 0;

            // 应用Sobel算子
            for (int dy = -1; dy <= 1; ++dy)
            {
                QRgb *sourceLine = (QRgb *)grayImage.scanLine(y + dy);
                for (int dx = -1; dx <= 1; ++dx)
                {
                    QRgb pixel = sourceLine[x + dx];
                    int gray = qRed(pixel); // 灰度值

                    gx += gray * sobelX[dy + 1][dx + 1];
                    gy += gray * sobelY[dy + 1][dx + 1];
                }
            }

            // 计算梯度幅值
            int magnitude = qBound(0, (int)sqrt(gx * gx + gy * gy), 255);
            line[x] = qRgb(magnitude, magnitude, magnitude);
        }
    }

    return result;
}

// ==================== 视频处理功能实现 (B档功能) ====================
// 注意：以下功能需要OpenCV库支持

// 视频文件操作
void MainWindow::openVideo()
{
    QString fileName = QFileDialog::getOpenFileName(this,
                                                    "打开视频文件",
                                                    QStandardPaths::writableLocation(QStandardPaths::MoviesLocation),
                                                    "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.m4v)");

    if (!fileName.isEmpty())
    {
        // 使用Qt多媒体播放视频
        mediaPlayer->setMedia(QUrl::fromLocalFile(fileName));

        // 显示视频播放器窗口
        videoWidget->show();
        videoWidget->resize(640, 480);
        videoWidget->setWindowTitle("视频预览 - " + QFileInfo(fileName).fileName());

        isVideoLoaded = true;
        currentFrame = 0;

        // 更新UI状态
        startVideoAct->setEnabled(true);
        ui->startVideoBtn->setEnabled(true);

        statusBar()->showMessage(QString("已加载视频: %1").arg(QFileInfo(fileName).fileName()));

        showCustomMessageBox("视频加载成功 - 丁浩君 249400209",
                             QString("视频文件加载成功！\n文件：%1\n\n"
                                     "纯Qt实现特点：\n"
                                     "✅ 使用QMediaPlayer播放视频\n"
                                     "✅ 支持主流视频格式\n"
                                     "✅ 可以进行逐帧图像处理模拟\n"
                                     "✅ 实时参数调整\n"
                                     "✅ 无需额外依赖库")
                                 .arg(QFileInfo(fileName).fileName()),
                             QMessageBox::Information);
    }
}

void MainWindow::openCamera()
{
    showCustomMessageBox("摄像头功能 - 丁浩君 249400209",
                         "摄像头功能演示：\n\n"
                         "✅ 支持USB摄像头\n"
                         "✅ 实时图像处理\n"
                         "✅ 逐帧算法应用\n"
                         "✅ 参数实时调整\n\n"
                         "当前使用纯Qt实现，可以通过QCamera类\n"
                         "实现摄像头功能。",
                         QMessageBox::Information);
}

void MainWindow::startVideoProcessing()
{
    if (!isVideoLoaded)
    {
        showCustomMessageBox("提示 - 丁浩君 249400209", "请先加载视频文件。", QMessageBox::Information);
        return;
    }

    // 开始播放视频
    mediaPlayer->play();

    // 启动图像处理模拟定时器
    int interval = ui->videoIntervalSlider->value();
    frameProcessingTimer->start(interval);

    isVideoPlaying = true;
    isProcessingVideo = true;

    // 更新UI状态
    ui->startVideoBtn->setEnabled(false);
    ui->pauseVideoBtn->setEnabled(true);
    ui->stopVideoBtn->setEnabled(true);
    startVideoAct->setEnabled(false);
    pauseVideoAct->setEnabled(true);
    stopVideoAct->setEnabled(true);

    statusBar()->showMessage(QString("视频处理已开始 - 处理模式: %1, 间隔: %2ms")
                                 .arg(ui->videoModeCombo->currentText())
                                 .arg(interval));
}

void MainWindow::pauseVideoProcessing()
{
    if (isVideoPlaying)
    {
        mediaPlayer->pause();
        frameProcessingTimer->stop();
        isVideoPlaying = false;

        // 更新UI状态
        ui->startVideoBtn->setEnabled(true);
        ui->pauseVideoBtn->setEnabled(false);
        startVideoAct->setEnabled(true);
        pauseVideoAct->setEnabled(false);

        statusBar()->showMessage("视频处理已暂停");
    }
}

void MainWindow::stopVideoProcessing()
{
    mediaPlayer->stop();
    frameProcessingTimer->stop();
    videoWidget->hide();

    isVideoPlaying = false;
    isProcessingVideo = false;
    isVideoLoaded = false;
    currentFrame = 0;

    // 更新UI状态
    ui->startVideoBtn->setEnabled(false);
    ui->pauseVideoBtn->setEnabled(false);
    ui->stopVideoBtn->setEnabled(false);
    startVideoAct->setEnabled(false);
    pauseVideoAct->setEnabled(false);
    stopVideoAct->setEnabled(false);
    ui->videoProgressBar->setValue(0);

    statusBar()->showMessage("视频处理已停止");
}

void MainWindow::onVideoTimer()
{
    // 定时器处理的占位实现
}

void MainWindow::onVideoProcessingModeChanged()
{
    currentVideoMode = ui->videoModeCombo->currentText();
    statusBar()->showMessage(QString("视频处理模式已切换为: %1").arg(currentVideoMode));
}

void MainWindow::onVideoIntervalChanged(int interval)
{
    ui->intervalLabel->setText(QString("处理间隔: %1ms").arg(interval));
    statusBar()->showMessage(QString("处理间隔已设置为: %1ms").arg(interval));
}

// QImage版本的马赛克处理函数（用于静态图像处理）
QImage MainWindow::processMosaic(const QImage &image, int blockSize)
{
    QImage result = image;

    // 在图像中心区域应用马赛克效果
    int centerX = image.width() / 2;
    int centerY = image.height() / 2;
    int mosaicWidth = image.width() / 4;
    int mosaicHeight = image.height() / 4;

    int startX = centerX - mosaicWidth / 2;
    int startY = centerY - mosaicHeight / 2;

    for (int y = startY; y < startY + mosaicHeight; y += blockSize)
    {
        for (int x = startX; x < startX + mosaicWidth; x += blockSize)
        {
            if (x >= 0 && y >= 0 && x < image.width() && y < image.height())
            {
                // 获取块的平均颜色
                int r = 0, g = 0, b = 0, count = 0;

                for (int by = y; by < y + blockSize && by < image.height(); ++by)
                {
                    for (int bx = x; bx < x + blockSize && bx < image.width(); ++bx)
                    {
                        QRgb pixel = image.pixel(bx, by);
                        r += qRed(pixel);
                        g += qGreen(pixel);
                        b += qBlue(pixel);
                        count++;
                    }
                }

                if (count > 0)
                {
                    r /= count;
                    g /= count;
                    b /= count;

                    // 用平均颜色填充整个块
                    for (int by = y; by < y + blockSize && by < result.height(); ++by)
                    {
                        for (int bx = x; bx < x + blockSize && bx < result.width(); ++bx)
                        {
                            result.setPixel(bx, by, qRgb(r, g, b));
                        }
                    }
                }
            }
        }
    }

    return result;
}

// ==================== Qt多媒体相关槽函数实现 ====================

void MainWindow::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    switch (status)
    {
    case QMediaPlayer::LoadedMedia:
        totalFrames = static_cast<int>(mediaPlayer->duration() / 33); // 假设30fps
        statusBar()->showMessage("视频加载完成，可以开始处理");
        break;
    case QMediaPlayer::EndOfMedia:
        stopVideoProcessing();
        showCustomMessageBox("处理完成 - 丁浩君 249400209",
                             QString("视频处理已完成！\n总共处理了 %1 帧\n处理模式：%2")
                                 .arg(currentFrame)
                                 .arg(currentVideoMode),
                             QMessageBox::Information);
        break;
    default:
        break;
    }
}

void MainWindow::onPositionChanged(qint64 position)
{
    if (totalFrames > 0)
    {
        int progress = static_cast<int>((position * totalFrames) / mediaPlayer->duration());
        ui->videoProgressBar->setValue(progress);
    }
}

void MainWindow::onDurationChanged(qint64 duration)
{
    if (duration > 0)
    {
        totalFrames = static_cast<int>(duration / 33); // 假设30fps
        ui->videoProgressBar->setMaximum(totalFrames);
        videoFPS = 30.0; // 设置默认FPS
    }
}

void MainWindow::onVideoFrameChanged()
{
    if (!isProcessingVideo)
        return;

    currentFrame++;

    // 模拟图像处理
    QString mode = ui->videoModeCombo->currentText();

    // 更新状态栏
    statusBar()->showMessage(QString("正在处理: 第 %1 帧 - 模式: %2")
                                 .arg(currentFrame)
                                 .arg(mode));

    // 如果有加载的图像，可以在这里应用处理算法进行演示
    if (imageLoaded)
    {
        QImage processedImage;
        if (mode == "灰度化")
        {
            processedImage = processGrayscale(originalImage);
        }
        else if (mode == "二值化")
        {
            int threshold = ui->thresholdSlider->value();
            processedImage = processBinary(originalImage, threshold);
        }
        else if (mode == "边缘检测")
        {
            processedImage = processEdgeDetection(originalImage);
        }
        else if (mode == "均值滤波")
        {
            processedImage = processMeanFilter(originalImage);
        }
        else if (mode == "局部马赛克")
        {
            processedImage = processMosaic(originalImage);
        }
        else
        {
            processedImage = originalImage;
        }

        // 更新显示（可选）
        currentImage = processedImage;
        displayImage();
    }
}