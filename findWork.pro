QT       += core gui multimedia

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

# OpenCV 配置 - 视频处理功能 (暂时注释以确保编译通过)
# 如果您已安装OpenCV，请取消注释以下配置：
# Windows 配置：
win32 {
    OPENCV_DIR = C:/opencv/build
    INCLUDEPATH += $$OPENCV_DIR/include

    CONFIG(debug, debug|release) {
        LIBS += -L$$OPENCV_DIR/x64/vc16/lib \
                -lopencv_core4d \
                -lopencv_imgproc4d \
                -lopencv_imgcodecs4d \
                -lopencv_videoio4d \
                -lopencv_highgui4d
    } else {
        LIBS += -L$$OPENCV_DIR/x64/vc16/lib \
                -lopencv_core4 \
                -lopencv_imgproc4 \
                -lopencv_imgcodecs4 \
                -lopencv_videoio4 \
                -lopencv_highgui4
    }
}

# Linux 示例配置：
#unix:!macx {
#    CONFIG += link_pkgconfig
#    PKGCONFIG += opencv4
#}

# macOS 示例配置：
#macx {
#    INCLUDEPATH += /usr/local/include/opencv4
#    LIBS += -L/usr/local/lib \
#            -lopencv_core \
#            -lopencv_imgproc \
#            -lopencv_imgcodecs \
#            -lopencv_videoio \
#            -lopencv_highgui
#}

# 设置应用程序名称和版本 - 丁浩君249400209
TARGET = ImageProcessor_DingHaoJun_249400209
VERSION = 1.0

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    main.cpp \
    mainwindow.cpp

HEADERS += \
    mainwindow.h

FORMS += \
    mainwindow.ui

RESOURCES += \
    resources.qrc

# 设置应用程序图标（暂时注释，需要添加实际图标文件）
# RC_ICONS = images/icons/app_icon.ico

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
