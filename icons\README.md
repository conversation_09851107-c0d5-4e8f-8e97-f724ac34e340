# 图标文件说明

## 当前状态
为了避免编译错误，所有图标引用已被暂时注释。软件可以正常编译和运行，但没有图标显示。

## 如何添加图标

### 第一步：准备图标文件
在此目录下添加以下图标文件：
- `app_icon.ico` - 应用程序主图标（32x32或48x48像素）
- `open.png` - 打开文件图标（16x16或24x24像素）
- `save.png` - 保存文件图标（16x16或24x24像素）
- `exit.png` - 退出程序图标（16x16或24x24像素）
- `about.png` - 关于对话框图标（16x16或24x24像素）
- `grayscale.png` - 灰度化处理图标（16x16或24x24像素）
- `binary.png` - 二值化处理图标（16x16或24x24像素）
- `filter.png` - 滤波处理图标（16x16或24x24像素）
- `gamma.png` - 伽马变换图标（16x16或24x24像素）
- `edge.png` - 边缘检测图标（16x16或24x24像素）

### 第二步：修改资源文件
在 `resources.qrc` 文件中取消注释图标引用：
```xml
<RCC>
    <qresource prefix="/icons">
        <file>icons/open.png</file>
        <file>icons/save.png</file>
        <file>icons/exit.png</file>
        <file>icons/about.png</file>
        <file>icons/grayscale.png</file>
        <file>icons/binary.png</file>
        <file>icons/filter.png</file>
        <file>icons/gamma.png</file>
        <file>icons/edge.png</file>
        <file>icons/app_icon.ico</file>
    </qresource>
</RCC>
```

### 第三步：修改项目文件
在 `findWork.pro` 文件中取消注释应用程序图标：
```
RC_ICONS = icons/app_icon.ico
```

### 第四步：修改代码
在 `mainwindow.cpp` 文件中取消注释所有图标引用。

## 图标获取方法

### 方法一：使用本地图标生成器
1. 打开 `icons/create_icons.html` 文件（用浏览器打开）
2. 点击各个图标下方的"下载"按钮获取SVG文件
3. 使用在线转换工具将SVG转换为PNG格式：
   - https://convertio.co/zh/svg-png/
   - https://www.aconvert.com/cn/image/svg-to-png/
4. 调整图标尺寸为16x16或24x24像素

### 方法二：从推荐网站下载
- **阿里巴巴矢量图标库** (https://www.iconfont.cn/)
  - 注册登录后可免费下载
  - 支持PNG、SVG、ICO格式
  - 图标质量高，种类丰富

- **Icons8** (https://icons8.com/)
  - 免费版本有水印，付费版无水印
  - 支持多种格式和尺寸

- **Flaticon** (https://www.flaticon.com/)
  - 需要注册，免费版需要署名
  - 图标质量很高

- **Icon-icons** (https://icon-icons.com/zh/)
  - 完全免费
  - 支持PNG、SVG、ICO格式

### 方法三：使用Bing搜索
1. 搜索关键词：`图标 PNG 16x16 打开文件` 等
2. 选择"图片"搜索结果
3. 筛选条件：透明背景、小尺寸
4. 右键保存图片

## 注意事项
- 确保图标文件格式正确（PNG/ICO）
- 建议使用透明背景的PNG图标
- 图标尺寸要适中，避免过大影响界面美观
