# Icons 目录说明

这个目录存放数字图像处理软件的所有图标文件，是 resources.qrc 中引用的标准图标目录。

## 需要的图标文件

### 应用程序图标
- **app_icon.ico** - 应用程序主图标（多尺寸ICO文件）

### 菜单和工具栏图标 (16x16 或 24x24 像素)
- **open.png** - 打开文件图标
- **save.png** - 保存文件图标
- **exit.png** - 退出程序图标
- **about.png** - 关于对话框图标
- **关于.png** - 自定义消息对话框图标（丁浩君 249400209专用）

### 图像处理工具图标 (24x24 像素)
- **grayscale.png** - 灰度化处理图标
- **binary.png** - 二值化处理图标
- **filter.png** - 滤波处理图标
- **gamma.png** - 伽马变换图标
- **edge.png** - 边缘检测图标

## 推荐图标来源

1. **阿里巴巴矢量图标库** (https://www.iconfont.cn/)
2. **Icons8** (https://icons8.com/)
3. **Flaticon** (https://www.flaticon.com/)
4. **Material Design Icons**
5. **Font Awesome**

## 安装步骤

1. 下载图标文件到此目录
2. 在 resources.qrc 中取消注释相应引用
3. 在代码中取消注释图标加载语句
4. 重新编译项目
