#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QLabel>
#include <QScrollArea>
#include <QSlider>
#include <QSpinBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QPushButton>
#include <QFileDialog>
#include <QMessageBox>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QAction>
#include <QPixmap>
#include <QImage>
#include <QImageReader>
#include <QImageWriter>
#include <QStandardPaths>
#include <QDir>
#include <QSplitter>
#include <QDockWidget>
#include <QTimer>
#include <QProgressBar>
#include <QComboBox>
#include <QCheckBox>

// OpenCV 头文件 - 视频处理功能 (暂时注释以避免编译错误)
// 如果您已安装OpenCV，请取消注释以下行：
/*
#ifdef _WIN32
#pragma warning(push)
#pragma warning(disable : 4996)
#endif
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/videoio.hpp>
#ifdef _WIN32
#pragma warning(pop)
#endif
*/

// OpenCV前向声明 (避免编译错误)
namespace cv
{
    class Mat;
    class VideoCapture;
}

QT_BEGIN_NAMESPACE
namespace Ui
{
    class MainWindow;
}
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // 文件操作槽函数
    void openImage();
    void openMultipleImages();
    void saveImage();
    void exitApplication();

    // 图像处理槽函数
    void convertToGrayscale();
    void convertToBinary();
    void applyMeanFilter();
    void applyGammaCorrection();
    void detectEdges();

    // 界面控制槽函数
    void onThresholdChanged(int value);
    void onGammaChanged(double value);
    void resetImage();
    void fitToWindow();
    void actualSize();
    void zoomIn();
    void zoomOut();

    // 帮助和关于
    void showAbout();

    // 自定义消息框函数
    void showCustomMessageBox(const QString &title, const QString &text, QMessageBox::Icon icon);

    // 视频处理槽函数
    void openVideo();
    void openCamera();
    void startVideoProcessing();
    void stopVideoProcessing();
    void pauseVideoProcessing();
    void onVideoTimer();
    void onVideoProcessingModeChanged();
    void onVideoIntervalChanged(int interval);

private:
    // UI相关
    Ui::MainWindow *ui;
    void setupUI();
    void createMenus();
    void createToolBars();
    void createStatusBar();
    void connectSignals();

    // 图像处理相关
    void displayImage();
    void updateImageDisplay();
    void scaleImage(double factor);
    QImage processGrayscale(const QImage &image);
    QImage processBinary(const QImage &image, int threshold);
    QImage processMeanFilter(const QImage &image);
    QImage processGammaCorrection(const QImage &image, double gamma);
    QImage processEdgeDetection(const QImage &image);

    // 视频处理相关函数 (暂时注释以避免编译错误)
    // 如果您已安装OpenCV，请取消注释以下行：
    /*
    QImage matToQImage(const cv::Mat &mat);
    cv::Mat qImageToMat(const QImage &qimg);
    cv::Mat processVideoFrame(const cv::Mat &frame, const QString &mode);
    */
    QImage processMosaic(const QImage &image, int blockSize = 10);

    // 成员变量
    QImage originalImage;
    QImage currentImage;

    // 菜单和工具栏
    QMenu *fileMenu;
    QMenu *editMenu;
    QMenu *viewMenu;
    QMenu *helpMenu;
    QToolBar *fileToolBar;
    QToolBar *editToolBar;
    QToolBar *viewToolBar;

    // 动作
    QAction *openAct;
    QAction *openMultipleAct;
    QAction *saveAct;
    QAction *exitAct;
    QAction *grayscaleAct;
    QAction *binaryAct;
    QAction *meanFilterAct;
    QAction *gammaAct;
    QAction *edgeDetectionAct;
    QAction *resetAct;
    QAction *fitToWindowAct;
    QAction *actualSizeAct;
    QAction *zoomInAct;
    QAction *zoomOutAct;
    QAction *aboutAct;

    // 状态和缩放
    double scaleFactor;
    bool imageLoaded;

    // 视频处理相关成员变量
    void *videoCapture; // 使用void指针避免编译错误，实际使用时需要转换为cv::VideoCapture*
    QTimer *videoTimer;

    // 视频处理状态
    bool isVideoLoaded;
    bool isVideoPlaying;
    bool isProcessingVideo;
    int currentFrame;
    int totalFrames;
    double videoFPS;
    QString currentVideoMode;

    // 视频处理菜单和动作
    QMenu *videoMenu;
    QAction *openVideoAct;
    QAction *openCameraAct;
    QAction *startVideoAct;
    QAction *stopVideoAct;
    QAction *pauseVideoAct;
};
#endif // MAINWINDOW_H
