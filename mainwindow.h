#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QLabel>
#include <QScrollArea>
#include <QSlider>
#include <QSpinBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QPushButton>
#include <QFileDialog>
#include <QMessageBox>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QAction>
#include <QPixmap>
#include <QImage>
#include <QImageReader>
#include <QImageWriter>
#include <QStandardPaths>
#include <QDir>
#include <QSplitter>
#include <QDockWidget>

QT_BEGIN_NAMESPACE
namespace Ui
{
    class MainWindow;
}
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // 文件操作槽函数
    void openImage();
    void openMultipleImages();
    void saveImage();
    void exitApplication();

    // 图像处理槽函数
    void convertToGrayscale();
    void convertToBinary();
    void applyMeanFilter();
    void applyGammaCorrection();
    void detectEdges();

    // 界面控制槽函数
    void onThresholdChanged(int value);
    void onGammaChanged(double value);
    void resetImage();
    void fitToWindow();
    void actualSize();
    void zoomIn();
    void zoomOut();

    // 帮助和关于
    void showAbout();

private:
    // UI相关
    Ui::MainWindow *ui;
    void setupUI();
    void createMenus();
    void createToolBars();
    void createStatusBar();
    void createDockWidgets();
    void connectSignals();

    // 图像处理相关
    void displayImage();
    void updateImageDisplay();
    void scaleImage(double factor);
    QImage processGrayscale(const QImage &image);
    QImage processBinary(const QImage &image, int threshold);
    QImage processMeanFilter(const QImage &image);
    QImage processGammaCorrection(const QImage &image, double gamma);
    QImage processEdgeDetection(const QImage &image);

    // 成员变量
    QImage originalImage;
    QImage currentImage;
    QLabel *imageLabel;
    QScrollArea *scrollArea;
    QSplitter *mainSplitter;

    // 工具面板
    QDockWidget *toolDock;
    QWidget *toolPanel;

    // 控制面板
    QDockWidget *controlDock;
    QSlider *thresholdSlider;
    QSpinBox *thresholdSpinBox;
    QSlider *gammaSlider;
    QLabel *gammaValueLabel;

    // 工具按钮
    QPushButton *grayscaleBtn;
    QPushButton *binaryBtn;
    QPushButton *meanFilterBtn;
    QPushButton *gammaBtn;
    QPushButton *edgeDetectionBtn;
    QPushButton *resetBtn;

    // 菜单和工具栏
    QMenu *fileMenu;
    QMenu *editMenu;
    QMenu *viewMenu;
    QMenu *helpMenu;
    QToolBar *fileToolBar;
    QToolBar *editToolBar;
    QToolBar *viewToolBar;

    // 动作
    QAction *openAct;
    QAction *openMultipleAct;
    QAction *saveAct;
    QAction *exitAct;
    QAction *grayscaleAct;
    QAction *binaryAct;
    QAction *meanFilterAct;
    QAction *gammaAct;
    QAction *edgeDetectionAct;
    QAction *resetAct;
    QAction *fitToWindowAct;
    QAction *actualSizeAct;
    QAction *zoomInAct;
    QAction *zoomOutAct;
    QAction *aboutAct;

    // 状态和缩放
    double scaleFactor;
    bool imageLoaded;
};
#endif // MAINWINDOW_H
