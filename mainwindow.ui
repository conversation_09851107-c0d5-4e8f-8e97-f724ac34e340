<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>数字图像处理软件V1.0 丁浩君 249400209</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QScrollArea" name="imageScrollArea">
      <property name="widgetResizable">
       <bool>true</bool>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
      <widget class="QWidget" name="scrollAreaWidgetContents">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>69</width>
         <height>69</height>
        </rect>
       </property>
       <layout class="QVBoxLayout" name="imageLayout">
        <item>
         <widget class="QLabel" name="imageLabel">
          <property name="minimumSize">
           <size>
            <width>400</width>
            <height>300</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QLabel { border: 1px solid gray; background-color: white; }</string>
          </property>
          <property name="text">
           <string>请打开图片文件</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="toolFrame">
      <property name="minimumSize">
       <size>
        <width>120</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>120</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <property name="styleSheet">
       <string notr="true">QFrame#toolFrame {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
}

QPushButton {
    border: 1px solid #ccc;
    border-radius: 3px;
    background-color: #f0f0f0;
    font-size: 10px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #e0e0e0;
    border-color: #999;
}

QPushButton:pressed {
    background-color: #d0d0d0;
    border: 2px solid #666;
}

QPushButton:disabled {
    background-color: #f8f8f8;
    color: #ccc;
    border-color: #eee;
}

QLabel#toolLabel {
    font-weight: bold;
    color: #333;
}

QGroupBox {
    font-weight: bold;
    border: 1px solid #ccc;
    border-radius: 3px;
    margin-top: 10px;
    padding-top: 5px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}</string>
      </property>
      <layout class="QVBoxLayout" name="toolLayout">
       <property name="spacing">
        <number>5</number>
       </property>
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>10</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>10</number>
       </property>
       <item>
        <widget class="QLabel" name="toolLabel">
         <property name="text">
          <string>图像处理工具</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QGridLayout" name="toolGridLayout">
         <property name="spacing">
          <number>3</number>
         </property>
         <item row="0" column="0">
          <widget class="QPushButton" name="grayscaleBtn">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="toolTip">
            <string>灰度化 (Ctrl+G)</string>
           </property>
           <property name="text">
            <string>灰度</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QPushButton" name="binaryBtn">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="toolTip">
            <string>二值化 (Ctrl+B)</string>
           </property>
           <property name="text">
            <string>二值</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QPushButton" name="meanFilterBtn">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="toolTip">
            <string>均值滤波 (Ctrl+F)</string>
           </property>
           <property name="text">
            <string>滤波</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QPushButton" name="gammaBtn">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="toolTip">
            <string>伽马变换 (Ctrl+A)</string>
           </property>
           <property name="text">
            <string>伽马</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QPushButton" name="edgeDetectionBtn">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="toolTip">
            <string>边缘检测 (Ctrl+E)</string>
           </property>
           <property name="text">
            <string>边缘</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QPushButton" name="resetBtn">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="toolTip">
            <string>重置图片 (Ctrl+R)</string>
           </property>
           <property name="text">
            <string>重置</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="Line" name="line_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="binaryGroupBox">
         <property name="title">
          <string>二值化阈值</string>
         </property>
         <layout class="QVBoxLayout" name="binaryLayout">
          <item>
           <layout class="QHBoxLayout" name="thresholdLayout">
            <item>
             <widget class="QLabel" name="thresholdLabel">
              <property name="text">
               <string>阈值:</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QSpinBox" name="thresholdSpinBox">
              <property name="maximum">
               <number>255</number>
              </property>
              <property name="value">
               <number>128</number>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QSlider" name="thresholdSlider">
            <property name="maximum">
             <number>255</number>
            </property>
            <property name="value">
             <number>128</number>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="gammaGroupBox">
         <property name="title">
          <string>伽马变换</string>
         </property>
         <layout class="QVBoxLayout" name="gammaLayout">
          <item>
           <widget class="QLabel" name="gammaValueLabel">
            <property name="text">
             <string>伽马值: 1.0</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QSlider" name="gammaSlider">
            <property name="minimum">
             <number>10</number>
            </property>
            <property name="maximum">
             <number>300</number>
            </property>
            <property name="value">
             <number>100</number>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1200</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
