# OpenCV 配置指南

## 📋 概述

视频处理功能需要OpenCV库支持。本指南将帮助您在不同平台上安装和配置OpenCV。

## 🪟 Windows 配置 (推荐)

### 方法一：预编译版本 (推荐)

#### 1. 下载OpenCV
- 访问 https://opencv.org/releases/
- 下载 OpenCV 4.x Windows 版本
- 推荐下载 `opencv-4.8.0-windows.exe`

#### 2. 安装OpenCV
```bash
# 解压到指定目录，例如：
C:\opencv\
```

#### 3. 配置项目文件
在 `findWork.pro` 中已经预配置了Windows路径：
```qmake
win32 {
    OPENCV_DIR = C:/opencv/build
    INCLUDEPATH += $$OPENCV_DIR/include
    
    CONFIG(debug, debug|release) {
        LIBS += -L$$OPENCV_DIR/x64/vc16/lib \
                -lopencv_core4d \
                -lopencv_imgproc4d \
                -lopencv_imgcodecs4d \
                -lopencv_videoio4d \
                -lopencv_highgui4d
    } else {
        LIBS += -L$$OPENCV_DIR/x64/vc16/lib \
                -lopencv_core4 \
                -lopencv_imgproc4 \
                -lopencv_imgcodecs4 \
                -lopencv_videoio4 \
                -lopencv_highgui4
    }
}
```

#### 4. 修改路径（如果需要）
如果您的OpenCV安装在不同位置，请修改 `OPENCV_DIR` 路径。

#### 5. 添加DLL路径
将以下路径添加到系统PATH环境变量：
```
C:\opencv\build\x64\vc16\bin
```

### 方法二：vcpkg 包管理器

#### 1. 安装vcpkg
```bash
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat
```

#### 2. 安装OpenCV
```bash
.\vcpkg install opencv4[core,imgproc,imgcodecs,videoio,highgui]:x64-windows
```

#### 3. 集成到Visual Studio
```bash
.\vcpkg integrate install
```

## 🐧 Linux 配置

### Ubuntu/Debian
```bash
# 安装依赖
sudo apt update
sudo apt install build-essential cmake git pkg-config

# 安装OpenCV开发包
sudo apt install libopencv-dev python3-opencv

# 或者安装特定版本
sudo apt install libopencv-core-dev libopencv-imgproc-dev libopencv-imgcodecs-dev libopencv-videoio-dev libopencv-highgui-dev
```

### CentOS/RHEL/Fedora
```bash
# Fedora
sudo dnf install opencv-devel

# CentOS/RHEL (需要EPEL)
sudo yum install epel-release
sudo yum install opencv-devel
```

### 从源码编译 (高级用户)
```bash
# 下载源码
git clone https://github.com/opencv/opencv.git
cd opencv
mkdir build && cd build

# 配置编译
cmake -D CMAKE_BUILD_TYPE=Release \
      -D CMAKE_INSTALL_PREFIX=/usr/local \
      -D BUILD_EXAMPLES=OFF \
      ..

# 编译安装
make -j$(nproc)
sudo make install
```

## 🍎 macOS 配置

### 使用Homebrew (推荐)
```bash
# 安装Homebrew (如果未安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装OpenCV
brew install opencv

# 查看安装路径
brew --prefix opencv
```

### 使用MacPorts
```bash
sudo port install opencv4
```

## 🔧 验证安装

### 1. 检查OpenCV版本
创建测试文件 `test_opencv.cpp`：
```cpp
#include <opencv2/opencv.hpp>
#include <iostream>

int main() {
    std::cout << "OpenCV Version: " << CV_VERSION << std::endl;
    
    // 测试基本功能
    cv::Mat img = cv::Mat::zeros(100, 100, CV_8UC3);
    if (!img.empty()) {
        std::cout << "OpenCV 工作正常!" << std::endl;
    }
    
    return 0;
}
```

### 2. 编译测试
```bash
# Linux/macOS
g++ test_opencv.cpp -o test_opencv `pkg-config --cflags --libs opencv4`
./test_opencv

# Windows (使用Qt Creator或Visual Studio编译)
```

## 🚨 常见问题解决

### Windows 问题

#### 问题1：找不到OpenCV库
```
错误：cannot find -lopencv_core4
```
**解决方案：**
1. 检查 `OPENCV_DIR` 路径是否正确
2. 确认库文件存在于 `lib` 目录
3. 检查是否使用了正确的编译器版本 (vc16)

#### 问题2：运行时找不到DLL
```
错误：无法启动此程序，因为计算机中丢失 opencv_core4.dll
```
**解决方案：**
1. 将 `C:\opencv\build\x64\vc16\bin` 添加到PATH
2. 或者将DLL文件复制到可执行文件目录

#### 问题3：编译器版本不匹配
**解决方案：**
- vc14 = Visual Studio 2015
- vc15 = Visual Studio 2017  
- vc16 = Visual Studio 2019
- vc17 = Visual Studio 2022

选择与您的Qt编译器匹配的版本。

### Linux 问题

#### 问题1：pkg-config找不到opencv4
```bash
# 检查可用包
pkg-config --list-all | grep opencv

# 如果是opencv而不是opencv4
pkg-config --cflags --libs opencv
```

#### 问题2：权限问题
```bash
# 确保有读取权限
sudo chmod -R 755 /usr/local/include/opencv4
sudo ldconfig
```

### macOS 问题

#### 问题1：Homebrew路径问题
```bash
# 检查安装路径
brew --prefix opencv

# 更新项目文件中的路径
INCLUDEPATH += /opt/homebrew/include/opencv4  # Apple Silicon
INCLUDEPATH += /usr/local/include/opencv4     # Intel Mac
```

## 📝 项目配置模板

### 完整的 .pro 文件配置
```qmake
QT += core gui multimedia
CONFIG += c++11

# OpenCV 配置
win32 {
    OPENCV_DIR = C:/opencv/build
    INCLUDEPATH += $$OPENCV_DIR/include
    LIBS += -L$$OPENCV_DIR/x64/vc16/lib -lopencv_core4 -lopencv_imgproc4 -lopencv_videoio4
}

unix:!macx {
    CONFIG += link_pkgconfig
    PKGCONFIG += opencv4
}

macx {
    INCLUDEPATH += /usr/local/include/opencv4
    LIBS += -L/usr/local/lib -lopencv_core -lopencv_imgproc -lopencv_videoio
}
```

## ✅ 配置完成检查

配置完成后，请确认：

1. ✅ **编译通过**：项目可以成功编译
2. ✅ **链接成功**：没有链接错误
3. ✅ **运行正常**：程序可以启动
4. ✅ **视频功能**：可以打开视频菜单
5. ✅ **摄像头测试**：可以尝试打开摄像头

## 🎯 推荐配置

### 开发环境
- **Windows**: OpenCV 4.8.0 + Qt 6.x + MSVC 2019
- **Linux**: OpenCV 4.x + Qt 6.x + GCC
- **macOS**: OpenCV 4.x + Qt 6.x + Clang

### 最小配置
如果只需要基本功能，可以只安装：
- opencv_core
- opencv_imgproc  
- opencv_videoio

这样可以减少依赖和安装大小。

配置完成后，您就可以使用完整的视频处理功能了！
