# 数字图像处理软件项目结构

## 📁 完整目录结构

```
findWork/                           # 项目根目录
├── 📄 findWork.pro                 # Qt项目文件
├── 📄 findWork.pro.user            # Qt Creator用户配置
├── 📄 main.cpp                     # 程序入口文件
├── 📄 mainwindow.h                 # 主窗口头文件
├── 📄 mainwindow.cpp               # 主窗口实现文件
├── 📄 mainwindow.ui                # 主窗口UI设计文件
├── 📄 resources.qrc                # 资源文件配置
├── 📄 README.md                    # 项目说明文档
├── 📄 使用说明.md                   # 用户使用手册
├── 📄 启用图标指南.md               # 图标配置指南
├── 📄 项目结构说明.md               # 本文件
├── 📁 icons/                       # 图标文件夹（旧版本兼容）
│   ├── 📄 README.md
│   ├── 📄 create_icons.html        # 图标生成器
│   └── 📄 placeholder.txt
└── 📁 images/                      # 图片资源文件夹（标准结构）
    ├── 📄 README.md
    ├── 📁 icons/                   # 图标子文件夹
    │   ├── 📄 README.md
    │   ├── 📁 toolbar/             # 工具栏图标
    │   ├── 📁 menu/                # 菜单图标
    │   └── 📁 app/                 # 应用程序图标
    ├── 📁 samples/                 # 示例图片
    │   └── 📄 README.md
    ├── 📁 backgrounds/             # 背景图片
    └── 📁 ui/                      # UI相关图片
```

## 📋 文件说明

### 核心代码文件
- **main.cpp**: 程序入口，设置应用程序信息
- **mainwindow.h**: 主窗口类声明，包含所有成员变量和函数声明
- **mainwindow.cpp**: 主窗口类实现，包含所有图像处理算法
- **mainwindow.ui**: UI界面设计文件，定义窗口布局和控件

### 配置文件
- **findWork.pro**: Qt项目配置文件，定义编译选项和依赖
- **resources.qrc**: 资源文件配置，管理图标和图片资源

### 文档文件
- **README.md**: 项目概述和功能介绍
- **使用说明.md**: 详细的用户操作手册
- **启用图标指南.md**: 图标配置和启用指南

### 资源文件夹

#### icons/ (旧版本兼容)
- 保留原有的图标文件夹结构
- 包含图标生成器和占位符文件

#### images/ (标准结构)
- **images/icons/**: 按类型分类的图标文件
  - `toolbar/`: 工具栏图标 (16x16, 24x24)
  - `menu/`: 菜单图标 (16x16)
  - `app/`: 应用程序图标 (多尺寸ICO)
- **images/samples/**: 测试用的示例图片
- **images/backgrounds/**: 背景图片
- **images/ui/**: UI装饰图片

## 🎯 资源管理策略

### 图标管理
1. **开发阶段**: 使用 `icons/` 文件夹的简单结构
2. **生产阶段**: 迁移到 `images/icons/` 的分类结构
3. **资源引用**: 通过 `resources.qrc` 统一管理

### 图片资源
- **示例图片**: 放在 `images/samples/` 用于测试
- **UI图片**: 放在 `images/ui/` 用于界面美化
- **背景图片**: 放在 `images/backgrounds/` 用于主题

## 🔧 使用方法

### 添加图标
1. 将图标文件放入 `images/icons/` 相应子文件夹
2. 在 `resources.qrc` 中添加文件引用
3. 在代码中使用 `QIcon(":/icons/...")` 加载

### 添加示例图片
1. 将图片放入 `images/samples/`
2. 在 `resources.qrc` 中添加引用
3. 可在程序中提供"打开示例"功能

### 资源路径规范
- 图标: `:/icons/toolbar/open.png`
- 示例图片: `:/images/samples/lena.jpg`
- UI图片: `:/ui/background.png`

## 📦 部署注意事项

### 资源文件
- 所有资源都会编译到可执行文件中
- 注意控制资源文件总大小
- 大图片建议放在外部文件夹

### 图标要求
- 工具栏图标: PNG格式，透明背景
- 应用程序图标: ICO格式，多尺寸
- 保持图标风格一致

## 🎨 设计建议

### 图标设计
- 使用统一的设计语言
- 保持简洁明了
- 适配不同DPI显示

### 示例图片
- 提供多种类型的测试图片
- 包含不同尺寸和格式
- 考虑算法测试需求

这个结构确保了项目的可维护性和扩展性，符合Qt项目的最佳实践。
