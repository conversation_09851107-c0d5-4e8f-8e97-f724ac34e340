# 示例图片文件夹

这里存放用于测试图像处理功能的示例图片。

## 推荐的测试图片

### 1. 经典测试图片
- **Lena.jpg** - 经典的图像处理测试图片
- **Baboon.jpg** - 高频细节丰富的图片
- **Peppers.jpg** - 彩色测试图片

### 2. 几何图形
- **checkerboard.png** - 棋盘图案
- **circles.png** - 圆形图案
- **lines.png** - 直线图案

### 3. 特殊用途
- **gradient.png** - 渐变图，测试伽马变换
- **noise.jpg** - 有噪声的图片，测试滤波
- **text.png** - 文字图片，测试边缘检测
- **high_contrast.png** - 高对比度图片，测试二值化

## 图片要求

- **格式**：JPG, PNG, BMP, TIFF
- **尺寸**：建议 512x512 或 256x256 像素
- **大小**：单个文件不超过 2MB
- **内容**：适合测试各种图像处理算法

## 获取方式

1. **免费图片网站**：
   - Unsplash (https://unsplash.com/)
   - Pixabay (https://pixabay.com/)
   - Pexels (https://www.pexels.com/)

2. **学术资源**：
   - USC-SIPI Image Database
   - ImageNet (部分免费)

3. **自制图片**：
   - 使用画图软件创建几何图形
   - 拍摄照片并调整尺寸

## 使用建议

- 准备不同类型的图片测试不同功能
- 保留原始图片用于对比效果
- 定期更新测试图片集
